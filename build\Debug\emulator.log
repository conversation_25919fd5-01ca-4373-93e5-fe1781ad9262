[2025-07-15 07:11:10.251] [info] Starting PS4 Emulator
[2025-07-15 07:11:10.259] [info] LoadSettings: Loaded from C:\Users\<USER>\AppData\Roaming\PS4Emulator\settings.ini, latency=8230us
[2025-07-15 07:11:10.647] [info] Vulkan context initialization complete, waiting for device idle...
[2025-07-15 07:11:10.647] [info] Vulkan device idle, starting emulator initialization
[2025-07-15 07:11:10.647] [info] PS4Emulator constructed
[2025-07-15 07:11:10.647] [info] Initializing PS4 emulator asynchronously
[2025-07-15 07:11:10.661] [info] Applied display settings: width=1938, height=1084, fullscreen=false, ui_scale=1.046
[2025-07-15 07:11:10.748] [info] === PS4 Emulator Initialization Started ===
[2025-07-15 07:11:10.748] [info] Step 1/10: Validating system requirements...
[2025-07-15 07:11:10.748] [warning] Low disk space: 10623459328 bytes available
[2025-07-15 07:11:10.748] [info] Step 2/10: Creating component instances...
[2025-07-15 07:11:10.748] [info] Constructing PS4MMU (default)...
[2025-07-15 07:11:10.748] [info] Attempting to allocate 1024 MB of physical memory...
[2025-07-15 07:11:11.023] [info] Successfully allocated 1024 MB of physical memory
[2025-07-15 07:11:11.023] [info] Constructing PhysicalMemoryAllocator...
[2025-07-15 07:11:11.023] [info] PhysicalMemoryAllocator constructed with empty block lists
[2025-07-15 07:11:11.024] [info] Initializing PhysicalMemoryAllocator with size 0x40000000
[2025-07-15 07:11:11.024] [info] Initialized with free block: start=0x1000, size=0x3ffff000 (reserved 0x0-0x1000)
[2025-07-15 07:11:11.024] [info] Constructing SwapManager: path='ps4_swap.bin', maxSize=0x20000000
[2025-07-15 07:11:11.024] [info] SwapManager constructed
[2025-07-15 07:11:11.024] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.024] [debug] All compressed pages cleared
[2025-07-15 07:11:11.024] [info] Memory compressor initialized with algorithm 0 and policy 1
[2025-07-15 07:11:11.024] [info] Initializing SwapManager: path='ps4_swap.bin'
[2025-07-15 07:11:11.024] [info] SwapManager initialized
[2025-07-15 07:11:11.024] [info] PS4MMU: Initialized memory stats - total pages: 262144, free pages: 262144
[2025-07-15 07:11:11.025] [info] PS4MMU default constructed with size: 0x40000000 bytes
[2025-07-15 07:11:11.025] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.025] [info] OrbisOS constructed
[2025-07-15 07:11:11.025] [info] PS4Filesystem constructed
[2025-07-15 07:11:11.025] [info] SyscallHandler constructed
[2025-07-15 07:11:11.025] [info] PS4ControllerManager constructed
[2025-07-15 07:11:11.026] [info] AudioDevice constructed
[2025-07-15 07:11:11.026] [info] PS4Audio constructed
[2025-07-15 07:11:11.026] [info] PS4TSC constructed
[2025-07-15 07:11:11.026] [info] GNMShaderTranslator constructed
[2025-07-15 07:11:11.026] [info] GNMRegisterState constructed with zero-initialized registers and stats.
[2025-07-15 07:11:11.026] [debug] GNMShaderTranslator: SPIR-V callback set
[2025-07-15 07:11:11.026] [debug] GNMShaderTranslator: GLSL callback set
[2025-07-15 07:11:11.026] [debug] GNMRegisterState: Register change callback set.
[2025-07-15 07:11:11.026] [info] PS4GPU constructed
[2025-07-15 07:11:11.027] [info] CommandProcessor constructed with zero-initialized stats
[2025-07-15 07:11:11.027] [info] FiberManager constructed
[2025-07-15 07:11:11.027] [info] TrophyManager constructed
[2025-07-15 07:11:11.027] [info] ZlibWrapper constructed
[2025-07-15 07:11:11.027] [error] ReadFile: Invalid path: /system/packages.json
[2025-07-15 07:11:11.027] [info] PKGInstaller initialized with filesystem, install root: /app
[2025-07-15 07:11:11.027] [info] JIT compiler initialized for CPU 0x28091b80 with zero-initialized stats
[2025-07-15 07:11:11.027] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.027] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.027] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.028] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.028] [info] JIT compiler initialized for CPU 0x28091b80 with zero-initialized stats
[2025-07-15 07:11:11.028] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-15 07:11:11.028] [info] Pipeline statistics reset
[2025-07-15 07:11:11.028] [info] Initialized 7 execution units
[2025-07-15 07:11:11.028] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-15 07:11:11.028] [debug] Pipeline: Register dependency tracking initialized
[2025-07-15 07:11:11.028] [info] Pipeline initialized for CPU at 0x28091b80 with 7 execution units and register dependency tracking
[2025-07-15 07:11:11.029] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.029] [info] X86_64CPU[0]: Starting register initialization
[2025-07-15 07:11:11.029] [debug] X86_64CPU[0]: Initializing SIMD registers
[2025-07-15 07:11:11.029] [debug] X86_64CPU[0]: Mask registers initialized
[2025-07-15 07:11:11.029] [debug] X86_64CPU[0]: AVX-512 not supported, registers zero-initialized
[2025-07-15 07:11:11.029] [info] X86_64CPU[0]: Creating devices
[2025-07-15 07:11:11.029] [debug] X86_64CPU[0]: Creating PIC
[2025-07-15 07:11:11.029] [info] PIC constructed
[2025-07-15 07:11:11.029] [debug] X86_64CPU[0]: Creating PIT
[2025-07-15 07:11:11.029] [info] PIT constructed
[2025-07-15 07:11:11.029] [debug] X86_64CPU[0]: Creating APIC
[2025-07-15 07:11:11.029] [info] APIC initialized for core 0
[2025-07-15 07:11:11.029] [info] X86_64CPU[0]: Registering devices
[2025-07-15 07:11:11.029] [debug] X86_64CPU[0]: Registering PIC at 0x20
[2025-07-15 07:11:11.029] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-15 07:11:11.029] [debug] X86_64CPU[0]: Registering PIT at 0x40
[2025-07-15 07:11:11.029] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-15 07:11:11.030] [debug] X86_64CPU[0]: Registering APIC at 0xFEE00000
[2025-07-15 07:11:11.030] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-15 07:11:11.030] [info] X86_64CPU[0] created with 16 XMM registers
[2025-07-15 07:11:11.030] [info] JIT compiler initialized for CPU 0x28091b80 with zero-initialized stats
[2025-07-15 07:11:11.030] [info] JIT compiler initialized for CPU 0x28111140 with zero-initialized stats
[2025-07-15 07:11:11.030] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.030] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.030] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.030] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.030] [info] JIT compiler initialized for CPU 0x28111140 with zero-initialized stats
[2025-07-15 07:11:11.030] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-15 07:11:11.031] [info] Pipeline statistics reset
[2025-07-15 07:11:11.031] [info] Initialized 7 execution units
[2025-07-15 07:11:11.031] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-15 07:11:11.031] [debug] Pipeline: Register dependency tracking initialized
[2025-07-15 07:11:11.031] [info] Pipeline initialized for CPU at 0x28111140 with 7 execution units and register dependency tracking
[2025-07-15 07:11:11.031] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.031] [info] X86_64CPU[1]: Starting register initialization
[2025-07-15 07:11:11.031] [debug] X86_64CPU[1]: Initializing SIMD registers
[2025-07-15 07:11:11.031] [debug] X86_64CPU[1]: Mask registers initialized
[2025-07-15 07:11:11.031] [debug] X86_64CPU[1]: AVX-512 not supported, registers zero-initialized
[2025-07-15 07:11:11.031] [info] X86_64CPU[1]: Creating devices
[2025-07-15 07:11:11.031] [debug] X86_64CPU[1]: Creating PIC
[2025-07-15 07:11:11.031] [info] PIC constructed
[2025-07-15 07:11:11.031] [debug] X86_64CPU[1]: Creating PIT
[2025-07-15 07:11:11.031] [info] PIT constructed
[2025-07-15 07:11:11.031] [debug] X86_64CPU[1]: Creating APIC
[2025-07-15 07:11:11.032] [info] APIC initialized for core 1
[2025-07-15 07:11:11.032] [info] X86_64CPU[1]: Registering devices
[2025-07-15 07:11:11.032] [debug] X86_64CPU[1]: Registering PIC at 0x20
[2025-07-15 07:11:11.032] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-15 07:11:11.032] [debug] X86_64CPU[1]: Registering PIT at 0x40
[2025-07-15 07:11:11.032] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-15 07:11:11.032] [debug] X86_64CPU[1]: Registering APIC at 0xFEE00000
[2025-07-15 07:11:11.032] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-15 07:11:11.032] [info] X86_64CPU[1] created with 16 XMM registers
[2025-07-15 07:11:11.032] [info] JIT compiler initialized for CPU 0x28111140 with zero-initialized stats
[2025-07-15 07:11:11.032] [info] JIT compiler initialized for CPU 0x2814c3c0 with zero-initialized stats
[2025-07-15 07:11:11.032] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.032] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.032] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.033] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.033] [info] JIT compiler initialized for CPU 0x2814c3c0 with zero-initialized stats
[2025-07-15 07:11:11.033] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-15 07:11:11.033] [info] Pipeline statistics reset
[2025-07-15 07:11:11.033] [info] Initialized 7 execution units
[2025-07-15 07:11:11.033] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-15 07:11:11.033] [debug] Pipeline: Register dependency tracking initialized
[2025-07-15 07:11:11.033] [info] Pipeline initialized for CPU at 0x2814c3c0 with 7 execution units and register dependency tracking
[2025-07-15 07:11:11.033] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.033] [info] X86_64CPU[2]: Starting register initialization
[2025-07-15 07:11:11.033] [debug] X86_64CPU[2]: Initializing SIMD registers
[2025-07-15 07:11:11.033] [debug] X86_64CPU[2]: Mask registers initialized
[2025-07-15 07:11:11.034] [debug] X86_64CPU[2]: AVX-512 not supported, registers zero-initialized
[2025-07-15 07:11:11.034] [info] X86_64CPU[2]: Creating devices
[2025-07-15 07:11:11.034] [debug] X86_64CPU[2]: Creating PIC
[2025-07-15 07:11:11.034] [info] PIC constructed
[2025-07-15 07:11:11.034] [debug] X86_64CPU[2]: Creating PIT
[2025-07-15 07:11:11.034] [info] PIT constructed
[2025-07-15 07:11:11.034] [debug] X86_64CPU[2]: Creating APIC
[2025-07-15 07:11:11.034] [info] APIC initialized for core 2
[2025-07-15 07:11:11.034] [info] X86_64CPU[2]: Registering devices
[2025-07-15 07:11:11.034] [debug] X86_64CPU[2]: Registering PIC at 0x20
[2025-07-15 07:11:11.034] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-15 07:11:11.034] [debug] X86_64CPU[2]: Registering PIT at 0x40
[2025-07-15 07:11:11.034] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-15 07:11:11.034] [debug] X86_64CPU[2]: Registering APIC at 0xFEE00000
[2025-07-15 07:11:11.034] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-15 07:11:11.034] [info] X86_64CPU[2] created with 16 XMM registers
[2025-07-15 07:11:11.034] [info] JIT compiler initialized for CPU 0x2814c3c0 with zero-initialized stats
[2025-07-15 07:11:11.034] [info] JIT compiler initialized for CPU 0x2819c080 with zero-initialized stats
[2025-07-15 07:11:11.035] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.035] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.035] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.035] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.035] [info] JIT compiler initialized for CPU 0x2819c080 with zero-initialized stats
[2025-07-15 07:11:11.035] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-15 07:11:11.035] [info] Pipeline statistics reset
[2025-07-15 07:11:11.035] [info] Initialized 7 execution units
[2025-07-15 07:11:11.035] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-15 07:11:11.036] [debug] Pipeline: Register dependency tracking initialized
[2025-07-15 07:11:11.036] [info] Pipeline initialized for CPU at 0x2819c080 with 7 execution units and register dependency tracking
[2025-07-15 07:11:11.036] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.036] [info] X86_64CPU[3]: Starting register initialization
[2025-07-15 07:11:11.036] [debug] X86_64CPU[3]: Initializing SIMD registers
[2025-07-15 07:11:11.036] [debug] X86_64CPU[3]: Mask registers initialized
[2025-07-15 07:11:11.036] [debug] X86_64CPU[3]: AVX-512 not supported, registers zero-initialized
[2025-07-15 07:11:11.036] [info] X86_64CPU[3]: Creating devices
[2025-07-15 07:11:11.036] [debug] X86_64CPU[3]: Creating PIC
[2025-07-15 07:11:11.036] [info] PIC constructed
[2025-07-15 07:11:11.036] [debug] X86_64CPU[3]: Creating PIT
[2025-07-15 07:11:11.036] [info] PIT constructed
[2025-07-15 07:11:11.036] [debug] X86_64CPU[3]: Creating APIC
[2025-07-15 07:11:11.036] [info] APIC initialized for core 3
[2025-07-15 07:11:11.036] [info] X86_64CPU[3]: Registering devices
[2025-07-15 07:11:11.036] [debug] X86_64CPU[3]: Registering PIC at 0x20
[2025-07-15 07:11:11.036] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-15 07:11:11.036] [debug] X86_64CPU[3]: Registering PIT at 0x40
[2025-07-15 07:11:11.037] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-15 07:11:11.037] [debug] X86_64CPU[3]: Registering APIC at 0xFEE00000
[2025-07-15 07:11:11.037] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-15 07:11:11.037] [info] X86_64CPU[3] created with 16 XMM registers
[2025-07-15 07:11:11.037] [info] JIT compiler initialized for CPU 0x2819c080 with zero-initialized stats
[2025-07-15 07:11:11.037] [info] JIT compiler initialized for CPU 0x281df680 with zero-initialized stats
[2025-07-15 07:11:11.037] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.037] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.037] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.037] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.037] [info] JIT compiler initialized for CPU 0x281df680 with zero-initialized stats
[2025-07-15 07:11:11.037] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-15 07:11:11.038] [info] Pipeline statistics reset
[2025-07-15 07:11:11.038] [info] Initialized 7 execution units
[2025-07-15 07:11:11.038] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-15 07:11:11.038] [debug] Pipeline: Register dependency tracking initialized
[2025-07-15 07:11:11.038] [info] Pipeline initialized for CPU at 0x281df680 with 7 execution units and register dependency tracking
[2025-07-15 07:11:11.038] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.038] [info] X86_64CPU[4]: Starting register initialization
[2025-07-15 07:11:11.038] [debug] X86_64CPU[4]: Initializing SIMD registers
[2025-07-15 07:11:11.038] [debug] X86_64CPU[4]: Mask registers initialized
[2025-07-15 07:11:11.038] [debug] X86_64CPU[4]: AVX-512 not supported, registers zero-initialized
[2025-07-15 07:11:11.038] [info] X86_64CPU[4]: Creating devices
[2025-07-15 07:11:11.038] [debug] X86_64CPU[4]: Creating PIC
[2025-07-15 07:11:11.038] [info] PIC constructed
[2025-07-15 07:11:11.038] [debug] X86_64CPU[4]: Creating PIT
[2025-07-15 07:11:11.039] [info] PIT constructed
[2025-07-15 07:11:11.039] [debug] X86_64CPU[4]: Creating APIC
[2025-07-15 07:11:11.039] [info] APIC initialized for core 4
[2025-07-15 07:11:11.039] [info] X86_64CPU[4]: Registering devices
[2025-07-15 07:11:11.039] [debug] X86_64CPU[4]: Registering PIC at 0x20
[2025-07-15 07:11:11.039] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-15 07:11:11.039] [debug] X86_64CPU[4]: Registering PIT at 0x40
[2025-07-15 07:11:11.039] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-15 07:11:11.039] [debug] X86_64CPU[4]: Registering APIC at 0xFEE00000
[2025-07-15 07:11:11.039] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-15 07:11:11.039] [info] X86_64CPU[4] created with 16 XMM registers
[2025-07-15 07:11:11.039] [info] JIT compiler initialized for CPU 0x281df680 with zero-initialized stats
[2025-07-15 07:11:11.039] [info] JIT compiler initialized for CPU 0x2820d840 with zero-initialized stats
[2025-07-15 07:11:11.039] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.039] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.039] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.040] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.040] [info] JIT compiler initialized for CPU 0x2820d840 with zero-initialized stats
[2025-07-15 07:11:11.040] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-15 07:11:11.040] [info] Pipeline statistics reset
[2025-07-15 07:11:11.040] [info] Initialized 7 execution units
[2025-07-15 07:11:11.040] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-15 07:11:11.040] [debug] Pipeline: Register dependency tracking initialized
[2025-07-15 07:11:11.040] [info] Pipeline initialized for CPU at 0x2820d840 with 7 execution units and register dependency tracking
[2025-07-15 07:11:11.040] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.040] [info] X86_64CPU[5]: Starting register initialization
[2025-07-15 07:11:11.040] [debug] X86_64CPU[5]: Initializing SIMD registers
[2025-07-15 07:11:11.040] [debug] X86_64CPU[5]: Mask registers initialized
[2025-07-15 07:11:11.040] [debug] X86_64CPU[5]: AVX-512 not supported, registers zero-initialized
[2025-07-15 07:11:11.040] [info] X86_64CPU[5]: Creating devices
[2025-07-15 07:11:11.040] [debug] X86_64CPU[5]: Creating PIC
[2025-07-15 07:11:11.041] [info] PIC constructed
[2025-07-15 07:11:11.041] [debug] X86_64CPU[5]: Creating PIT
[2025-07-15 07:11:11.041] [info] PIT constructed
[2025-07-15 07:11:11.041] [debug] X86_64CPU[5]: Creating APIC
[2025-07-15 07:11:11.041] [info] APIC initialized for core 5
[2025-07-15 07:11:11.041] [info] X86_64CPU[5]: Registering devices
[2025-07-15 07:11:11.041] [debug] X86_64CPU[5]: Registering PIC at 0x20
[2025-07-15 07:11:11.041] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-15 07:11:11.041] [debug] X86_64CPU[5]: Registering PIT at 0x40
[2025-07-15 07:11:11.041] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-15 07:11:11.041] [debug] X86_64CPU[5]: Registering APIC at 0xFEE00000
[2025-07-15 07:11:11.041] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-15 07:11:11.041] [info] X86_64CPU[5] created with 16 XMM registers
[2025-07-15 07:11:11.041] [info] JIT compiler initialized for CPU 0x2820d840 with zero-initialized stats
[2025-07-15 07:11:11.041] [info] JIT compiler initialized for CPU 0x2827ec80 with zero-initialized stats
[2025-07-15 07:11:11.042] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.042] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.042] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.042] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.042] [info] JIT compiler initialized for CPU 0x2827ec80 with zero-initialized stats
[2025-07-15 07:11:11.042] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-15 07:11:11.042] [info] Pipeline statistics reset
[2025-07-15 07:11:11.042] [info] Initialized 7 execution units
[2025-07-15 07:11:11.042] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-15 07:11:11.043] [debug] Pipeline: Register dependency tracking initialized
[2025-07-15 07:11:11.043] [info] Pipeline initialized for CPU at 0x2827ec80 with 7 execution units and register dependency tracking
[2025-07-15 07:11:11.043] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.043] [info] X86_64CPU[6]: Starting register initialization
[2025-07-15 07:11:11.043] [debug] X86_64CPU[6]: Initializing SIMD registers
[2025-07-15 07:11:11.043] [debug] X86_64CPU[6]: Mask registers initialized
[2025-07-15 07:11:11.043] [debug] X86_64CPU[6]: AVX-512 not supported, registers zero-initialized
[2025-07-15 07:11:11.043] [info] X86_64CPU[6]: Creating devices
[2025-07-15 07:11:11.043] [debug] X86_64CPU[6]: Creating PIC
[2025-07-15 07:11:11.043] [info] PIC constructed
[2025-07-15 07:11:11.043] [debug] X86_64CPU[6]: Creating PIT
[2025-07-15 07:11:11.043] [info] PIT constructed
[2025-07-15 07:11:11.043] [debug] X86_64CPU[6]: Creating APIC
[2025-07-15 07:11:11.043] [info] APIC initialized for core 6
[2025-07-15 07:11:11.043] [info] X86_64CPU[6]: Registering devices
[2025-07-15 07:11:11.043] [debug] X86_64CPU[6]: Registering PIC at 0x20
[2025-07-15 07:11:11.043] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-15 07:11:11.043] [debug] X86_64CPU[6]: Registering PIT at 0x40
[2025-07-15 07:11:11.043] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-15 07:11:11.043] [debug] X86_64CPU[6]: Registering APIC at 0xFEE00000
[2025-07-15 07:11:11.044] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-15 07:11:11.044] [info] X86_64CPU[6] created with 16 XMM registers
[2025-07-15 07:11:11.044] [info] JIT compiler initialized for CPU 0x2827ec80 with zero-initialized stats
[2025-07-15 07:11:11.044] [info] JIT compiler initialized for CPU 0x282a85c0 with zero-initialized stats
[2025-07-15 07:11:11.044] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.044] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.044] [info] InstructionDecoder constructed with lazy initialization
[2025-07-15 07:11:11.044] [info] InstructionDecoder ready (using lazy initialization for advanced instruction sets)
[2025-07-15 07:11:11.044] [info] JIT compiler initialized for CPU 0x282a85c0 with zero-initialized stats
[2025-07-15 07:11:11.044] [info] Two-level adaptive branch predictor initialized: Local tables: 1024, Global table: 4096, Choice table: 4096, BTB: 512, RAS: 32
[2025-07-15 07:11:11.045] [info] Pipeline statistics reset
[2025-07-15 07:11:11.045] [info] Initialized 7 execution units
[2025-07-15 07:11:11.045] [info] Initialized opcode dispatch table with 4 handlers
[2025-07-15 07:11:11.045] [debug] Pipeline: Register dependency tracking initialized
[2025-07-15 07:11:11.045] [info] Pipeline initialized for CPU at 0x282a85c0 with 7 execution units and register dependency tracking
[2025-07-15 07:11:11.045] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.045] [info] X86_64CPU[7]: Starting register initialization
[2025-07-15 07:11:11.047] [debug] X86_64CPU[7]: Initializing SIMD registers
[2025-07-15 07:11:11.047] [debug] X86_64CPU[7]: Mask registers initialized
[2025-07-15 07:11:11.047] [debug] X86_64CPU[7]: AVX-512 not supported, registers zero-initialized
[2025-07-15 07:11:11.047] [info] X86_64CPU[7]: Creating devices
[2025-07-15 07:11:11.047] [debug] X86_64CPU[7]: Creating PIC
[2025-07-15 07:11:11.047] [info] PIC constructed
[2025-07-15 07:11:11.047] [debug] X86_64CPU[7]: Creating PIT
[2025-07-15 07:11:11.047] [info] PIT constructed
[2025-07-15 07:11:11.047] [debug] X86_64CPU[7]: Creating APIC
[2025-07-15 07:11:11.047] [info] APIC initialized for core 7
[2025-07-15 07:11:11.047] [info] X86_64CPU[7]: Registering devices
[2025-07-15 07:11:11.047] [debug] X86_64CPU[7]: Registering PIC at 0x20
[2025-07-15 07:11:11.047] [info] DeviceManager: Successfully registered device at 0x20-0x21 (size: 0x2)
[2025-07-15 07:11:11.047] [debug] X86_64CPU[7]: Registering PIT at 0x40
[2025-07-15 07:11:11.047] [info] DeviceManager: Successfully registered device at 0x40-0x43 (size: 0x4)
[2025-07-15 07:11:11.048] [debug] X86_64CPU[7]: Registering APIC at 0xFEE00000
[2025-07-15 07:11:11.048] [info] DeviceManager: Successfully registered device at 0xfee00000-0xfee00fff (size: 0x1000)
[2025-07-15 07:11:11.048] [info] X86_64CPU[7] created with 16 XMM registers
[2025-07-15 07:11:11.048] [info] JIT compiler initialized for CPU 0x282a85c0 with zero-initialized stats
[2025-07-15 07:11:11.048] [info] InterruptHandler initialized for CPU 0
[2025-07-15 07:11:11.048] [info] Step 3/10: Initializing core components...
[2025-07-15 07:11:11.048] [info] Initializing PS4MMU...
[2025-07-15 07:11:11.048] [info] PS4MMU: Allocating physical memory buffer of size 0x40000000 bytes
[2025-07-15 07:11:11.048] [info] PS4MMU: Physical memory buffer allocated successfully
[2025-07-15 07:11:11.048] [info] Constructing PhysicalMemoryAllocator...
[2025-07-15 07:11:11.048] [info] PhysicalMemoryAllocator constructed with empty block lists
[2025-07-15 07:11:11.048] [info] PS4MMU: Initializing PhysicalMemoryAllocator with size 0x40000000
[2025-07-15 07:11:11.048] [info] Initializing PhysicalMemoryAllocator with size 0x40000000
[2025-07-15 07:11:11.048] [info] Initialized with free block: start=0x1000, size=0x3ffff000 (reserved 0x0-0x1000)
[2025-07-15 07:11:11.048] [info] PS4MMU: PhysicalMemoryAllocator initialized successfully
[2025-07-15 07:11:11.048] [info] PS4MMU: Testing PhysicalMemoryAllocator with a small allocation...
[2025-07-15 07:11:11.048] [info] PS4MMU: Test allocation successful, got address 0x1000
[2025-07-15 07:11:11.048] [info] PS4MMU: Test free successful
[2025-07-15 07:11:11.049] [info] Memory prefetcher initialized
[2025-07-15 07:11:11.049] [debug] All compressed pages cleared
[2025-07-15 07:11:11.049] [info] Memory compressor initialized with algorithm 0 and policy 1
[2025-07-15 07:11:11.049] [info] Constructing SwapManager: path='ps4_swap.bin', maxSize=0x20000000
[2025-07-15 07:11:11.049] [info] SwapManager constructed
[2025-07-15 07:11:11.049] [info] Destructing SwapManager...
[2025-07-15 07:11:11.049] [info] Shutting down SwapManager...
[2025-07-15 07:11:11.049] [info] SwapManager shutdown completed
[2025-07-15 07:11:11.049] [info] SwapManager destructed
[2025-07-15 07:11:11.049] [info] Initializing SwapManager: path='ps4_swap.bin'
[2025-07-15 07:11:11.049] [info] SwapManager initialized
[2025-07-15 07:11:11.050] [info] TLB initialized with max entries: 64
[2025-07-15 07:11:11.050] [info] PS4MMU: Initialized memory stats - total pages: 262144, free pages: 262144
[2025-07-15 07:11:11.050] [info] PS4MMU initialized with size: 0x40000000 bytes
[2025-07-15 07:11:11.050] [info] MMU initialized successfully
[2025-07-15 07:11:11.050] [info] PS4TSC calibrated: elapsed=8000ns, increment=80 cycles, host_freq=10000000 Hz, drift=8000.00%, latency=0us
[2025-07-15 07:11:11.050] [info] PS4TSC initialized with CPU frequency: 10000000 Hz, latency=180us
[2025-07-15 07:11:11.050] [info] TSC initialized successfully
[2025-07-15 07:11:11.050] [info] Initializing Filesystem with 45s timeout...
[2025-07-15 07:11:11.051] [info] Mounted ./ps4_root to /app0
[2025-07-15 07:11:11.051] [info] Mounted ./ps4_root\installed_packages to /mnt/sandbox/pfsmnt
[2025-07-15 07:11:11.051] [info] Mounted ./ps4_root\dev to /dev
[2025-07-15 07:11:11.051] [info] Mounted ./ps4_root\savedata to /savedata
[2025-07-15 07:11:11.051] [info] Mounted ./ps4_root\trophy to /trophy
[2025-07-15 07:11:11.051] [info] Mounted ./ps4_root\system to /system
[2025-07-15 07:11:11.051] [info] Initializing device files...
[2025-07-15 07:11:11.051] [info] Starting device file initialization...
[2025-07-15 07:11:11.051] [info] Creating standard device files...
[2025-07-15 07:11:11.051] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/null'
[2025-07-15 07:11:11.052] [debug] MapToHostPathLocked: Starting mapping for '/dev/null'
[2025-07-15 07:11:11.052] [debug] GetHostPath: Constructed path './ps4_root\dev\null' from mount './ps4_root\dev' + remaining 'null'
[2025-07-15 07:11:11.052] [debug] MapToHostPathLocked: virtualPath='/dev/null', mountPoint result='./ps4_root\dev\null'
[2025-07-15 07:11:11.052] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\null'
[2025-07-15 07:11:11.052] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\null' for '/dev/null'
[2025-07-15 07:11:11.052] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/null'
[2025-07-15 07:11:11.052] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/zero'
[2025-07-15 07:11:11.052] [debug] MapToHostPathLocked: Starting mapping for '/dev/zero'
[2025-07-15 07:11:11.052] [debug] GetHostPath: Constructed path './ps4_root\dev\zero' from mount './ps4_root\dev' + remaining 'zero'
[2025-07-15 07:11:11.052] [debug] MapToHostPathLocked: virtualPath='/dev/zero', mountPoint result='./ps4_root\dev\zero'
[2025-07-15 07:11:11.052] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\zero'
[2025-07-15 07:11:11.052] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\zero' for '/dev/zero'
[2025-07-15 07:11:11.053] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/zero'
[2025-07-15 07:11:11.053] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/random'
[2025-07-15 07:11:11.053] [debug] MapToHostPathLocked: Starting mapping for '/dev/random'
[2025-07-15 07:11:11.053] [debug] GetHostPath: Constructed path './ps4_root\dev\random' from mount './ps4_root\dev' + remaining 'random'
[2025-07-15 07:11:11.053] [debug] MapToHostPathLocked: virtualPath='/dev/random', mountPoint result='./ps4_root\dev\random'
[2025-07-15 07:11:11.053] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\random'
[2025-07-15 07:11:11.053] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\random' for '/dev/random'
[2025-07-15 07:11:11.053] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/random'
[2025-07-15 07:11:11.053] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/urandom'
[2025-07-15 07:11:11.053] [debug] MapToHostPathLocked: Starting mapping for '/dev/urandom'
[2025-07-15 07:11:11.053] [debug] GetHostPath: Constructed path './ps4_root\dev\urandom' from mount './ps4_root\dev' + remaining 'urandom'
[2025-07-15 07:11:11.053] [debug] MapToHostPathLocked: virtualPath='/dev/urandom', mountPoint result='./ps4_root\dev\urandom'
[2025-07-15 07:11:11.053] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\urandom'
[2025-07-15 07:11:11.053] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\urandom' for '/dev/urandom'
[2025-07-15 07:11:11.053] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/urandom'
[2025-07-15 07:11:11.053] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/stdout'
[2025-07-15 07:11:11.054] [debug] MapToHostPathLocked: Starting mapping for '/dev/stdout'
[2025-07-15 07:11:11.054] [debug] GetHostPath: Constructed path './ps4_root\dev\stdout' from mount './ps4_root\dev' + remaining 'stdout'
[2025-07-15 07:11:11.054] [debug] MapToHostPathLocked: virtualPath='/dev/stdout', mountPoint result='./ps4_root\dev\stdout'
[2025-07-15 07:11:11.054] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\stdout'
[2025-07-15 07:11:11.054] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\stdout' for '/dev/stdout'
[2025-07-15 07:11:11.054] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/stdout'
[2025-07-15 07:11:11.054] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/stderr'
[2025-07-15 07:11:11.054] [debug] MapToHostPathLocked: Starting mapping for '/dev/stderr'
[2025-07-15 07:11:11.054] [debug] GetHostPath: Constructed path './ps4_root\dev\stderr' from mount './ps4_root\dev' + remaining 'stderr'
[2025-07-15 07:11:11.054] [debug] MapToHostPathLocked: virtualPath='/dev/stderr', mountPoint result='./ps4_root\dev\stderr'
[2025-07-15 07:11:11.054] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\stderr'
[2025-07-15 07:11:11.054] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\stderr' for '/dev/stderr'
[2025-07-15 07:11:11.054] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/stderr'
[2025-07-15 07:11:11.054] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/stdin'
[2025-07-15 07:11:11.054] [debug] MapToHostPathLocked: Starting mapping for '/dev/stdin'
[2025-07-15 07:11:11.055] [debug] GetHostPath: Constructed path './ps4_root\dev\stdin' from mount './ps4_root\dev' + remaining 'stdin'
[2025-07-15 07:11:11.055] [debug] MapToHostPathLocked: virtualPath='/dev/stdin', mountPoint result='./ps4_root\dev\stdin'
[2025-07-15 07:11:11.055] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\stdin'
[2025-07-15 07:11:11.055] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\stdin' for '/dev/stdin'
[2025-07-15 07:11:11.055] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/stdin'
[2025-07-15 07:11:11.055] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/console'
[2025-07-15 07:11:11.055] [debug] MapToHostPathLocked: Starting mapping for '/dev/console'
[2025-07-15 07:11:11.055] [debug] GetHostPath: Constructed path './ps4_root\dev\console' from mount './ps4_root\dev' + remaining 'console'
[2025-07-15 07:11:11.055] [debug] MapToHostPathLocked: virtualPath='/dev/console', mountPoint result='./ps4_root\dev\console'
[2025-07-15 07:11:11.055] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\console'
[2025-07-15 07:11:11.055] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\console' for '/dev/console'
[2025-07-15 07:11:11.055] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/console'
[2025-07-15 07:11:11.055] [info] Created standard I/O devices
[2025-07-15 07:11:11.055] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/dipsw'
[2025-07-15 07:11:11.055] [debug] MapToHostPathLocked: Starting mapping for '/dev/dipsw'
[2025-07-15 07:11:11.055] [debug] GetHostPath: Constructed path './ps4_root\dev\dipsw' from mount './ps4_root\dev' + remaining 'dipsw'
[2025-07-15 07:11:11.055] [debug] MapToHostPathLocked: virtualPath='/dev/dipsw', mountPoint result='./ps4_root\dev\dipsw'
[2025-07-15 07:11:11.055] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\dipsw'
[2025-07-15 07:11:11.056] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\dipsw' for '/dev/dipsw'
[2025-07-15 07:11:11.056] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/dipsw'
[2025-07-15 07:11:11.056] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/hid'
[2025-07-15 07:11:11.056] [debug] MapToHostPathLocked: Starting mapping for '/dev/hid'
[2025-07-15 07:11:11.056] [debug] GetHostPath: Constructed path './ps4_root\dev\hid' from mount './ps4_root\dev' + remaining 'hid'
[2025-07-15 07:11:11.056] [debug] MapToHostPathLocked: virtualPath='/dev/hid', mountPoint result='./ps4_root\dev\hid'
[2025-07-15 07:11:11.056] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\hid'
[2025-07-15 07:11:11.056] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\hid' for '/dev/hid'
[2025-07-15 07:11:11.056] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/hid'
[2025-07-15 07:11:11.056] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/gc'
[2025-07-15 07:11:11.056] [debug] MapToHostPathLocked: Starting mapping for '/dev/gc'
[2025-07-15 07:11:11.056] [debug] GetHostPath: Constructed path './ps4_root\dev\gc' from mount './ps4_root\dev' + remaining 'gc'
[2025-07-15 07:11:11.056] [debug] MapToHostPathLocked: virtualPath='/dev/gc', mountPoint result='./ps4_root\dev\gc'
[2025-07-15 07:11:11.056] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\gc'
[2025-07-15 07:11:11.056] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\gc' for '/dev/gc'
[2025-07-15 07:11:11.057] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/gc'
[2025-07-15 07:11:11.057] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/rng'
[2025-07-15 07:11:11.057] [debug] MapToHostPathLocked: Starting mapping for '/dev/rng'
[2025-07-15 07:11:11.057] [debug] GetHostPath: Constructed path './ps4_root\dev\rng' from mount './ps4_root\dev' + remaining 'rng'
[2025-07-15 07:11:11.057] [debug] MapToHostPathLocked: virtualPath='/dev/rng', mountPoint result='./ps4_root\dev\rng'
[2025-07-15 07:11:11.057] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\rng'
[2025-07-15 07:11:11.057] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\rng' for '/dev/rng'
[2025-07-15 07:11:11.057] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/rng'
[2025-07-15 07:11:11.057] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sbl_icc_mgr'
[2025-07-15 07:11:11.057] [debug] MapToHostPathLocked: Starting mapping for '/dev/sbl_icc_mgr'
[2025-07-15 07:11:11.057] [debug] GetHostPath: Constructed path './ps4_root\dev\sbl_icc_mgr' from mount './ps4_root\dev' + remaining 'sbl_icc_mgr'
[2025-07-15 07:11:11.057] [debug] MapToHostPathLocked: virtualPath='/dev/sbl_icc_mgr', mountPoint result='./ps4_root\dev\sbl_icc_mgr'
[2025-07-15 07:11:11.057] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sbl_icc_mgr'
[2025-07-15 07:11:11.057] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sbl_icc_mgr' for '/dev/sbl_icc_mgr'
[2025-07-15 07:11:11.057] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sbl_icc_mgr'
[2025-07-15 07:11:11.057] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sbl_core_mgr'
[2025-07-15 07:11:11.057] [debug] MapToHostPathLocked: Starting mapping for '/dev/sbl_core_mgr'
[2025-07-15 07:11:11.058] [debug] GetHostPath: Constructed path './ps4_root\dev\sbl_core_mgr' from mount './ps4_root\dev' + remaining 'sbl_core_mgr'
[2025-07-15 07:11:11.058] [debug] MapToHostPathLocked: virtualPath='/dev/sbl_core_mgr', mountPoint result='./ps4_root\dev\sbl_core_mgr'
[2025-07-15 07:11:11.058] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sbl_core_mgr'
[2025-07-15 07:11:11.058] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sbl_core_mgr' for '/dev/sbl_core_mgr'
[2025-07-15 07:11:11.058] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sbl_core_mgr'
[2025-07-15 07:11:11.058] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sbl_crypto_mgr'
[2025-07-15 07:11:11.058] [debug] MapToHostPathLocked: Starting mapping for '/dev/sbl_crypto_mgr'
[2025-07-15 07:11:11.058] [debug] GetHostPath: Constructed path './ps4_root\dev\sbl_crypto_mgr' from mount './ps4_root\dev' + remaining 'sbl_crypto_mgr'
[2025-07-15 07:11:11.058] [debug] MapToHostPathLocked: virtualPath='/dev/sbl_crypto_mgr', mountPoint result='./ps4_root\dev\sbl_crypto_mgr'
[2025-07-15 07:11:11.058] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sbl_crypto_mgr'
[2025-07-15 07:11:11.058] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sbl_crypto_mgr' for '/dev/sbl_crypto_mgr'
[2025-07-15 07:11:11.058] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sbl_crypto_mgr'
[2025-07-15 07:11:11.058] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sbl_auth_mgr'
[2025-07-15 07:11:11.059] [debug] MapToHostPathLocked: Starting mapping for '/dev/sbl_auth_mgr'
[2025-07-15 07:11:11.059] [debug] GetHostPath: Constructed path './ps4_root\dev\sbl_auth_mgr' from mount './ps4_root\dev' + remaining 'sbl_auth_mgr'
[2025-07-15 07:11:11.059] [debug] MapToHostPathLocked: virtualPath='/dev/sbl_auth_mgr', mountPoint result='./ps4_root\dev\sbl_auth_mgr'
[2025-07-15 07:11:11.059] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sbl_auth_mgr'
[2025-07-15 07:11:11.059] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sbl_auth_mgr' for '/dev/sbl_auth_mgr'
[2025-07-15 07:11:11.059] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sbl_auth_mgr'
[2025-07-15 07:11:11.059] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sbl_self_mgr'
[2025-07-15 07:11:11.059] [debug] MapToHostPathLocked: Starting mapping for '/dev/sbl_self_mgr'
[2025-07-15 07:11:11.059] [debug] GetHostPath: Constructed path './ps4_root\dev\sbl_self_mgr' from mount './ps4_root\dev' + remaining 'sbl_self_mgr'
[2025-07-15 07:11:11.059] [debug] MapToHostPathLocked: virtualPath='/dev/sbl_self_mgr', mountPoint result='./ps4_root\dev\sbl_self_mgr'
[2025-07-15 07:11:11.059] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sbl_self_mgr'
[2025-07-15 07:11:11.059] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sbl_self_mgr' for '/dev/sbl_self_mgr'
[2025-07-15 07:11:11.059] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sbl_self_mgr'
[2025-07-15 07:11:11.059] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sandbox_mgr'
[2025-07-15 07:11:11.059] [debug] MapToHostPathLocked: Starting mapping for '/dev/sandbox_mgr'
[2025-07-15 07:11:11.060] [debug] GetHostPath: Constructed path './ps4_root\dev\sandbox_mgr' from mount './ps4_root\dev' + remaining 'sandbox_mgr'
[2025-07-15 07:11:11.060] [debug] MapToHostPathLocked: virtualPath='/dev/sandbox_mgr', mountPoint result='./ps4_root\dev\sandbox_mgr'
[2025-07-15 07:11:11.060] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sandbox_mgr'
[2025-07-15 07:11:11.060] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sandbox_mgr' for '/dev/sandbox_mgr'
[2025-07-15 07:11:11.060] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sandbox_mgr'
[2025-07-15 07:11:11.060] [info] Created system management devices
[2025-07-15 07:11:11.060] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/vce'
[2025-07-15 07:11:11.060] [debug] MapToHostPathLocked: Starting mapping for '/dev/vce'
[2025-07-15 07:11:11.060] [debug] GetHostPath: Constructed path './ps4_root\dev\vce' from mount './ps4_root\dev' + remaining 'vce'
[2025-07-15 07:11:11.060] [debug] MapToHostPathLocked: virtualPath='/dev/vce', mountPoint result='./ps4_root\dev\vce'
[2025-07-15 07:11:11.060] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\vce'
[2025-07-15 07:11:11.060] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\vce' for '/dev/vce'
[2025-07-15 07:11:11.060] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/vce'
[2025-07-15 07:11:11.060] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/dmem'
[2025-07-15 07:11:11.060] [debug] MapToHostPathLocked: Starting mapping for '/dev/dmem'
[2025-07-15 07:11:11.060] [debug] GetHostPath: Constructed path './ps4_root\dev\dmem' from mount './ps4_root\dev' + remaining 'dmem'
[2025-07-15 07:11:11.061] [debug] MapToHostPathLocked: virtualPath='/dev/dmem', mountPoint result='./ps4_root\dev\dmem'
[2025-07-15 07:11:11.063] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\dmem'
[2025-07-15 07:11:11.063] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\dmem' for '/dev/dmem'
[2025-07-15 07:11:11.063] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/dmem'
[2025-07-15 07:11:11.063] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/sherlock'
[2025-07-15 07:11:11.063] [debug] MapToHostPathLocked: Starting mapping for '/dev/sherlock'
[2025-07-15 07:11:11.063] [debug] GetHostPath: Constructed path './ps4_root\dev\sherlock' from mount './ps4_root\dev' + remaining 'sherlock'
[2025-07-15 07:11:11.063] [debug] MapToHostPathLocked: virtualPath='/dev/sherlock', mountPoint result='./ps4_root\dev\sherlock'
[2025-07-15 07:11:11.063] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\sherlock'
[2025-07-15 07:11:11.063] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\sherlock' for '/dev/sherlock'
[2025-07-15 07:11:11.063] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/sherlock'
[2025-07-15 07:11:11.063] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/tty'
[2025-07-15 07:11:11.063] [debug] MapToHostPathLocked: Starting mapping for '/dev/tty'
[2025-07-15 07:11:11.063] [debug] GetHostPath: Constructed path './ps4_root\dev\tty' from mount './ps4_root\dev' + remaining 'tty'
[2025-07-15 07:11:11.063] [debug] MapToHostPathLocked: virtualPath='/dev/tty', mountPoint result='./ps4_root\dev\tty'
[2025-07-15 07:11:11.063] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\tty'
[2025-07-15 07:11:11.064] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\tty' for '/dev/tty'
[2025-07-15 07:11:11.064] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/tty'
[2025-07-15 07:11:11.064] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/ttyu0'
[2025-07-15 07:11:11.064] [debug] MapToHostPathLocked: Starting mapping for '/dev/ttyu0'
[2025-07-15 07:11:11.064] [debug] GetHostPath: Constructed path './ps4_root\dev\ttyu0' from mount './ps4_root\dev' + remaining 'ttyu0'
[2025-07-15 07:11:11.064] [debug] MapToHostPathLocked: virtualPath='/dev/ttyu0', mountPoint result='./ps4_root\dev\ttyu0'
[2025-07-15 07:11:11.064] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\ttyu0'
[2025-07-15 07:11:11.064] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\ttyu0' for '/dev/ttyu0'
[2025-07-15 07:11:11.064] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/ttyu0'
[2025-07-15 07:11:11.064] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/ttyu1'
[2025-07-15 07:11:11.064] [debug] MapToHostPathLocked: Starting mapping for '/dev/ttyu1'
[2025-07-15 07:11:11.064] [debug] GetHostPath: Constructed path './ps4_root\dev\ttyu1' from mount './ps4_root\dev' + remaining 'ttyu1'
[2025-07-15 07:11:11.064] [debug] MapToHostPathLocked: virtualPath='/dev/ttyu1', mountPoint result='./ps4_root\dev\ttyu1'
[2025-07-15 07:11:11.064] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\ttyu1'
[2025-07-15 07:11:11.064] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\ttyu1' for '/dev/ttyu1'
[2025-07-15 07:11:11.064] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/ttyu1'
[2025-07-15 07:11:11.065] [info] Created hardware interface devices
[2025-07-15 07:11:11.065] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/cd0'
[2025-07-15 07:11:11.065] [debug] MapToHostPathLocked: Starting mapping for '/dev/cd0'
[2025-07-15 07:11:11.065] [debug] GetHostPath: Constructed path './ps4_root\dev\cd0' from mount './ps4_root\dev' + remaining 'cd0'
[2025-07-15 07:11:11.065] [debug] MapToHostPathLocked: virtualPath='/dev/cd0', mountPoint result='./ps4_root\dev\cd0'
[2025-07-15 07:11:11.065] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\cd0'
[2025-07-15 07:11:11.065] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\cd0' for '/dev/cd0'
[2025-07-15 07:11:11.065] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/cd0'
[2025-07-15 07:11:11.065] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/da0'
[2025-07-15 07:11:11.065] [debug] MapToHostPathLocked: Starting mapping for '/dev/da0'
[2025-07-15 07:11:11.065] [debug] GetHostPath: Constructed path './ps4_root\dev\da0' from mount './ps4_root\dev' + remaining 'da0'
[2025-07-15 07:11:11.065] [debug] MapToHostPathLocked: virtualPath='/dev/da0', mountPoint result='./ps4_root\dev\da0'
[2025-07-15 07:11:11.065] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\da0'
[2025-07-15 07:11:11.065] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\da0' for '/dev/da0'
[2025-07-15 07:11:11.065] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/da0'
[2025-07-15 07:11:11.065] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/nvme0'
[2025-07-15 07:11:11.065] [debug] MapToHostPathLocked: Starting mapping for '/dev/nvme0'
[2025-07-15 07:11:11.066] [debug] GetHostPath: Constructed path './ps4_root\dev\nvme0' from mount './ps4_root\dev' + remaining 'nvme0'
[2025-07-15 07:11:11.066] [debug] MapToHostPathLocked: virtualPath='/dev/nvme0', mountPoint result='./ps4_root\dev\nvme0'
[2025-07-15 07:11:11.066] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\nvme0'
[2025-07-15 07:11:11.066] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\nvme0' for '/dev/nvme0'
[2025-07-15 07:11:11.066] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/nvme0'
[2025-07-15 07:11:11.066] [info] Created storage devices
[2025-07-15 07:11:11.066] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/gpu'
[2025-07-15 07:11:11.066] [debug] MapToHostPathLocked: Starting mapping for '/dev/gpu'
[2025-07-15 07:11:11.066] [debug] GetHostPath: Constructed path './ps4_root\dev\gpu' from mount './ps4_root\dev' + remaining 'gpu'
[2025-07-15 07:11:11.066] [debug] MapToHostPathLocked: virtualPath='/dev/gpu', mountPoint result='./ps4_root\dev\gpu'
[2025-07-15 07:11:11.066] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\gpu'
[2025-07-15 07:11:11.066] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\gpu' for '/dev/gpu'
[2025-07-15 07:11:11.066] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/gpu'
[2025-07-15 07:11:11.066] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/hdmi'
[2025-07-15 07:11:11.066] [debug] MapToHostPathLocked: Starting mapping for '/dev/hdmi'
[2025-07-15 07:11:11.066] [debug] GetHostPath: Constructed path './ps4_root\dev\hdmi' from mount './ps4_root\dev' + remaining 'hdmi'
[2025-07-15 07:11:11.066] [debug] MapToHostPathLocked: virtualPath='/dev/hdmi', mountPoint result='./ps4_root\dev\hdmi'
[2025-07-15 07:11:11.066] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\hdmi'
[2025-07-15 07:11:11.067] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\hdmi' for '/dev/hdmi'
[2025-07-15 07:11:11.067] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/hdmi'
[2025-07-15 07:11:11.067] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/audioout'
[2025-07-15 07:11:11.067] [debug] MapToHostPathLocked: Starting mapping for '/dev/audioout'
[2025-07-15 07:11:11.067] [debug] GetHostPath: Constructed path './ps4_root\dev\audioout' from mount './ps4_root\dev' + remaining 'audioout'
[2025-07-15 07:11:11.067] [debug] MapToHostPathLocked: virtualPath='/dev/audioout', mountPoint result='./ps4_root\dev\audioout'
[2025-07-15 07:11:11.067] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\audioout'
[2025-07-15 07:11:11.067] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\audioout' for '/dev/audioout'
[2025-07-15 07:11:11.067] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/audioout'
[2025-07-15 07:11:11.067] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/audioin'
[2025-07-15 07:11:11.067] [debug] MapToHostPathLocked: Starting mapping for '/dev/audioin'
[2025-07-15 07:11:11.067] [debug] GetHostPath: Constructed path './ps4_root\dev\audioin' from mount './ps4_root\dev' + remaining 'audioin'
[2025-07-15 07:11:11.067] [debug] MapToHostPathLocked: virtualPath='/dev/audioin', mountPoint result='./ps4_root\dev\audioin'
[2025-07-15 07:11:11.067] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\audioin'
[2025-07-15 07:11:11.067] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\audioin' for '/dev/audioin'
[2025-07-15 07:11:11.068] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/audioin'
[2025-07-15 07:11:11.068] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/bluetooth'
[2025-07-15 07:11:11.068] [debug] MapToHostPathLocked: Starting mapping for '/dev/bluetooth'
[2025-07-15 07:11:11.068] [debug] GetHostPath: Constructed path './ps4_root\dev\bluetooth' from mount './ps4_root\dev' + remaining 'bluetooth'
[2025-07-15 07:11:11.068] [debug] MapToHostPathLocked: virtualPath='/dev/bluetooth', mountPoint result='./ps4_root\dev\bluetooth'
[2025-07-15 07:11:11.068] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\bluetooth'
[2025-07-15 07:11:11.068] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\bluetooth' for '/dev/bluetooth'
[2025-07-15 07:11:11.068] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/bluetooth'
[2025-07-15 07:11:11.068] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/wlan'
[2025-07-15 07:11:11.068] [debug] MapToHostPathLocked: Starting mapping for '/dev/wlan'
[2025-07-15 07:11:11.068] [debug] GetHostPath: Constructed path './ps4_root\dev\wlan' from mount './ps4_root\dev' + remaining 'wlan'
[2025-07-15 07:11:11.068] [debug] MapToHostPathLocked: virtualPath='/dev/wlan', mountPoint result='./ps4_root\dev\wlan'
[2025-07-15 07:11:11.068] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\wlan'
[2025-07-15 07:11:11.068] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\wlan' for '/dev/wlan'
[2025-07-15 07:11:11.068] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/wlan'
[2025-07-15 07:11:11.068] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/usb'
[2025-07-15 07:11:11.068] [debug] MapToHostPathLocked: Starting mapping for '/dev/usb'
[2025-07-15 07:11:11.069] [debug] GetHostPath: Constructed path './ps4_root\dev\usb' from mount './ps4_root\dev' + remaining 'usb'
[2025-07-15 07:11:11.069] [debug] MapToHostPathLocked: virtualPath='/dev/usb', mountPoint result='./ps4_root\dev\usb'
[2025-07-15 07:11:11.069] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\usb'
[2025-07-15 07:11:11.069] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\usb' for '/dev/usb'
[2025-07-15 07:11:11.069] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/usb'
[2025-07-15 07:11:11.069] [info] Created media and I/O devices
[2025-07-15 07:11:11.069] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/camera'
[2025-07-15 07:11:11.069] [debug] MapToHostPathLocked: Starting mapping for '/dev/camera'
[2025-07-15 07:11:11.069] [debug] GetHostPath: Constructed path './ps4_root\dev\camera' from mount './ps4_root\dev' + remaining 'camera'
[2025-07-15 07:11:11.069] [debug] MapToHostPathLocked: virtualPath='/dev/camera', mountPoint result='./ps4_root\dev\camera'
[2025-07-15 07:11:11.069] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\camera'
[2025-07-15 07:11:11.069] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\camera' for '/dev/camera'
[2025-07-15 07:11:11.069] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/camera'
[2025-07-15 07:11:11.069] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/pad'
[2025-07-15 07:11:11.069] [debug] MapToHostPathLocked: Starting mapping for '/dev/pad'
[2025-07-15 07:11:11.069] [debug] GetHostPath: Constructed path './ps4_root\dev\pad' from mount './ps4_root\dev' + remaining 'pad'
[2025-07-15 07:11:11.070] [debug] MapToHostPathLocked: virtualPath='/dev/pad', mountPoint result='./ps4_root\dev\pad'
[2025-07-15 07:11:11.070] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\pad'
[2025-07-15 07:11:11.070] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\pad' for '/dev/pad'
[2025-07-15 07:11:11.070] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/pad'
[2025-07-15 07:11:11.070] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/touchpad'
[2025-07-15 07:11:11.070] [debug] MapToHostPathLocked: Starting mapping for '/dev/touchpad'
[2025-07-15 07:11:11.070] [debug] GetHostPath: Constructed path './ps4_root\dev\touchpad' from mount './ps4_root\dev' + remaining 'touchpad'
[2025-07-15 07:11:11.070] [debug] MapToHostPathLocked: virtualPath='/dev/touchpad', mountPoint result='./ps4_root\dev\touchpad'
[2025-07-15 07:11:11.070] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\touchpad'
[2025-07-15 07:11:11.070] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\touchpad' for '/dev/touchpad'
[2025-07-15 07:11:11.070] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/touchpad'
[2025-07-15 07:11:11.070] [debug] CreateDeviceFileInternalLocked: Starting for '/dev/motion'
[2025-07-15 07:11:11.070] [debug] MapToHostPathLocked: Starting mapping for '/dev/motion'
[2025-07-15 07:11:11.070] [debug] GetHostPath: Constructed path './ps4_root\dev\motion' from mount './ps4_root\dev' + remaining 'motion'
[2025-07-15 07:11:11.070] [debug] MapToHostPathLocked: virtualPath='/dev/motion', mountPoint result='./ps4_root\dev\motion'
[2025-07-15 07:11:11.071] [debug] MapToHostPathLocked: using mount point, result='./ps4_root\dev\motion'
[2025-07-15 07:11:11.071] [debug] CreateDeviceFileInternalLocked: Got host path './ps4_root\dev\motion' for '/dev/motion'
[2025-07-15 07:11:11.071] [debug] CreateDeviceFileInternalLocked: Successfully created device file '/dev/motion'
[2025-07-15 07:11:11.071] [info] Created input devices
[2025-07-15 07:11:11.071] [info] Registering device handlers...
[2025-07-15 07:11:11.071] [debug] Registered handler for device: /dev/null
[2025-07-15 07:11:11.071] [debug] Registered handler for device: /dev/zero
[2025-07-15 07:11:11.071] [debug] Registered handler for device: /dev/random
[2025-07-15 07:11:11.071] [debug] Registered handler for device: /dev/urandom
[2025-07-15 07:11:11.071] [info] Registered basic I/O device handlers
[2025-07-15 07:11:11.071] [debug] Registered handler for device: /dev/stdout
[2025-07-15 07:11:11.071] [debug] Registered handler for device: /dev/stderr
[2025-07-15 07:11:11.071] [debug] Registered handler for device: /dev/dipsw
[2025-07-15 07:11:11.071] [info] Registered standard I/O device handlers
[2025-07-15 07:11:11.071] [debug] Registered handler for device: /dev/sbl_icc_mgr
[2025-07-15 07:11:11.072] [debug] Registered handler for device: /dev/gc
[2025-07-15 07:11:11.072] [debug] Registered handler for device: /dev/pad
[2025-07-15 07:11:11.072] [debug] Registered handler for device: /dev/audioout
[2025-07-15 07:11:11.072] [info] Registered system device handlers
[2025-07-15 07:11:11.072] [info] Initialized 38 device files with handlers
[2025-07-15 07:11:11.072] [debug] Creating standard handles (FD 0, 1, 2)
[2025-07-15 07:11:11.072] [info] Device files initialized
[2025-07-15 07:11:11.072] [info] Initializing PFS...
[2025-07-15 07:11:11.072] [info] Initializing Protected File System (PFS)...
[2025-07-15 07:11:11.075] [debug] Created PFS directory: ./ps4_root\pfs
[2025-07-15 07:11:11.076] [debug] Created PFS directory: ./ps4_root\mnt/pfs
[2025-07-15 07:11:11.076] [debug] GetHostPath: Constructed path './ps4_root\installed_packages' from mount './ps4_root\installed_packages' + remaining ''
[2025-07-15 07:11:11.076] [debug] Created PFS directory: ./ps4_root\installed_packages
[2025-07-15 07:11:11.084] [info] Mounted ./ps4_root\pfs\app.pfs to /mnt/pfs/app
[2025-07-15 07:11:11.085] [info] Mounted PFS: ./ps4_root\pfs\app.pfs -> /mnt/pfs/app
[2025-07-15 07:11:11.085] [info] Mounted ./ps4_root\pfs\patch.pfs to /mnt/pfs/patch
[2025-07-15 07:11:11.085] [info] Mounted PFS: ./ps4_root\pfs\patch.pfs -> /mnt/pfs/patch
[2025-07-15 07:11:11.086] [info] Mounted ./ps4_root\pfs\system.pfs to /mnt/pfs/system
[2025-07-15 07:11:11.086] [info] Mounted PFS: ./ps4_root\pfs\system.pfs -> /mnt/pfs/system
[2025-07-15 07:11:11.087] [info] Mounted ./ps4_root\pfs\addcont.pfs to /mnt/pfs/addcont
[2025-07-15 07:11:11.087] [info] Mounted PFS: ./ps4_root\pfs\addcont.pfs -> /mnt/pfs/addcont
[2025-07-15 07:11:11.087] [info] PFS initialization completed successfully
[2025-07-15 07:11:11.087] [info] PFS initialized
[2025-07-15 07:11:11.087] [info] PS4Filesystem initialized with root path: ./ps4_root (took 37ms)
[2025-07-15 07:11:11.088] [info] Memory diagnostics reset
[2025-07-15 07:11:11.088] [info] MemoryDiagnostics initialized
[2025-07-15 07:11:11.092] [info] Filesystem initialized successfully in 41ms
[2025-07-15 07:11:11.093] [info] OrbisOS initialized
[2025-07-15 07:11:11.093] [info] OS initialized successfully
[2025-07-15 07:11:11.094] [info] IOManager initialized successfully
[2025-07-15 07:11:11.094] [info] Initializing FiberManager with 15s timeout...
[2025-07-15 07:11:11.094] [debug] Converting thread to fiber...
[2025-07-15 07:11:11.094] [debug] Successfully converted thread to fiber
[2025-07-15 07:11:11.094] [info] FiberManager initialized
[2025-07-15 07:11:11.104] [info] FiberManager initialized successfully in 10ms
[2025-07-15 07:11:11.104] [info] TrophyManager initialized for user default_user, latency=0us
[2025-07-15 07:11:11.104] [info] TrophyManager initialized successfully
[2025-07-15 07:11:11.104] [info] Step 4/10: Initializing graphics subsystem...
[2025-07-15 07:11:11.104] [info] Initializing TileManager with 10s timeout...
[2025-07-15 07:11:11.104] [info] TileManager initialized, latency=0us
[2025-07-15 07:11:11.114] [info] TileManager initialized successfully in 10ms
[2025-07-15 07:11:11.115] [info] Initializing GPU with 60s timeout...
[2025-07-15 07:11:11.115] [info] PS4GPU initializing...
[2025-07-15 07:11:11.115] [debug] Vulkan context validation passed
[2025-07-15 07:11:11.115] [info] PS4GPU: Creating swapchain...
[2025-07-15 07:11:11.115] [info] PS4GPU: Reusing existing swapchain created by main thread
[2025-07-15 07:11:11.115] [info] CreateSwapchain: Reused existing swapchain with 3 images, latency=345us
[2025-07-15 07:11:11.115] [info] PS4GPU: Creating swapchain image views...
[2025-07-15 07:11:11.115] [info] PS4GPU: Reusing existing swapchain image views created by main thread
[2025-07-15 07:11:11.115] [info] CreateSwapchainImageViews: Reused 3 existing image views, latency=431us
[2025-07-15 07:11:11.115] [info] PS4GPU: Creating command pool...
[2025-07-15 07:11:11.115] [info] PS4GPU: Reusing existing command pool created by main thread
[2025-07-15 07:11:11.115] [info] CreateCommandPool: Reused existing command pool, latency=464us
[2025-07-15 07:11:11.116] [info] PS4GPU: Creating descriptor pool...
[2025-07-15 07:11:11.116] [info] PS4GPU: Reusing existing descriptor pool created by main thread
[2025-07-15 07:11:11.116] [info] CreateDescriptorPool: Reused existing descriptor pool, latency=559us
[2025-07-15 07:11:11.116] [info] PS4GPU: Creating sync objects...
[2025-07-15 07:11:11.116] [info] PS4GPU: Reusing existing sync objects created by main thread
[2025-07-15 07:11:11.116] [info] CreateSyncObjects: Reused existing sync objects, latency=614us
[2025-07-15 07:11:11.116] [info] PS4GPU: Creating default render pass...
[2025-07-15 07:11:11.116] [info] PS4GPU: Reusing existing render pass created by main thread
[2025-07-15 07:11:11.116] [info] CreateDefaultRenderPass: Reused existing render pass, latency=688us
[2025-07-15 07:11:11.116] [info] PS4GPU: Creating framebuffers...
[2025-07-15 07:11:11.116] [info] CreateFramebuffers: Created 3 framebuffers, latency=701us
[2025-07-15 07:11:11.116] [info] PS4GPU initialized with Vulkan, latency=2162us
[2025-07-15 07:11:11.125] [info] GPU initialized successfully in 10ms
[2025-07-15 07:11:11.125] [info] Step 5/10: Initializing input and audio subsystems...
[2025-07-15 07:11:11.125] [info] PS4ControllerManager initialized
[2025-07-15 07:11:11.125] [info] ControllerManager initialized successfully
[2025-07-15 07:11:11.125] [info] Audio initialization: enabled=true
[2025-07-15 07:11:11.125] [info] Initializing Audio with 20s timeout...
[2025-07-15 07:11:11.126] [info] Starting PortAudio initialization with 3000ms timeout
[2025-07-15 07:11:11.290] [debug] Pa_Initialize completed in 164ms with result: 0
[2025-07-15 07:11:11.293] [info] PortAudio initialized successfully
[2025-07-15 07:11:11.296] [debug] Enumerated device 0: name=Microsoft Sound Mapper - Output, hostApi=2, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-15 07:11:11.296] [debug] Enumerated device 1: name=Realtek Digital Output (Realtek, hostApi=2, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-15 07:11:11.296] [debug] Enumerated device 2: name=Speakers (Realtek(R) Audio), hostApi=2, channels=8, sampleRate=44100, isBluetooth=false
[2025-07-15 07:11:11.296] [debug] Enumerated device 3: name=Primary Sound Driver, hostApi=1, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-15 07:11:11.297] [debug] Enumerated device 4: name=Realtek Digital Output (Realtek(R) Audio), hostApi=1, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-15 07:11:11.297] [debug] Enumerated device 5: name=Speakers (Realtek(R) Audio), hostApi=1, channels=8, sampleRate=44100, isBluetooth=false
[2025-07-15 07:11:11.297] [debug] Enumerated device 6: name=Speakers (Realtek(R) Audio), hostApi=13, channels=2, sampleRate=48000, isBluetooth=false
[2025-07-15 07:11:11.297] [debug] Enumerated device 7: name=Realtek Digital Output (Realtek(R) Audio), hostApi=13, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-15 07:11:11.297] [debug] Enumerated device 8: name=Output (AMD HD Audio DP out #5), hostApi=11, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-15 07:11:11.297] [debug] Skipping device 9: no output channels
[2025-07-15 07:11:11.297] [debug] Skipping device 10: no output channels
[2025-07-15 07:11:11.297] [debug] Enumerated device 11: name=SPDIF Out (Realtek HDA SPDIF Out), hostApi=11, channels=2, sampleRate=44100, isBluetooth=false
[2025-07-15 07:11:11.298] [debug] Skipping device 12: no output channels
[2025-07-15 07:11:11.298] [debug] Enumerated device 13: name=Speakers (Realtek HD Audio output), hostApi=11, channels=8, sampleRate=44100, isBluetooth=false
[2025-07-15 07:11:11.298] [info] Selected audio device: Speakers (Realtek(R) Audio) (index=6, hostApi=13)
[2025-07-15 07:11:11.298] [debug] Opening audio stream...
[2025-07-15 07:11:11.340] [info] Audio stream opened successfully
[2025-07-15 07:11:11.341] [info] AudioDevice initialized with device 6 (name=Speakers (Realtek(R) Audio))
[2025-07-15 07:11:11.341] [info] PS4Audio initialized: success=true
[2025-07-15 07:11:11.346] [info] Audio initialized successfully in 220ms
[2025-07-15 07:11:11.346] [info] Step 6/10: Initializing 8 CPU cores...
[2025-07-15 07:11:11.346] [info] Initializing CPU[0] with 20s timeout...
[2025-07-15 07:11:11.346] [info] Pipeline statistics reset
[2025-07-15 07:11:11.347] [info] X86_64CPU[0] initialized with full register support. RIP=0x1000
[2025-07-15 07:11:11.356] [info] CPU[0] initialized successfully in 10ms
[2025-07-15 07:11:11.357] [info] Initializing CPU[1] with 20s timeout...
[2025-07-15 07:11:11.357] [info] Pipeline statistics reset
[2025-07-15 07:11:11.357] [info] X86_64CPU[1] initialized with full register support. RIP=0x1000
[2025-07-15 07:11:11.367] [info] CPU[1] initialized successfully in 10ms
[2025-07-15 07:11:11.367] [info] Initializing CPU[2] with 20s timeout...
[2025-07-15 07:11:11.367] [info] Pipeline statistics reset
[2025-07-15 07:11:11.367] [info] X86_64CPU[2] initialized with full register support. RIP=0x1000
[2025-07-15 07:11:11.377] [info] CPU[2] initialized successfully in 10ms
[2025-07-15 07:11:11.378] [info] Initializing CPU[3] with 20s timeout...
[2025-07-15 07:11:11.378] [info] Pipeline statistics reset
[2025-07-15 07:11:11.378] [info] X86_64CPU[3] initialized with full register support. RIP=0x1000
[2025-07-15 07:11:11.388] [info] CPU[3] initialized successfully in 10ms
[2025-07-15 07:11:11.388] [info] Initializing CPU[4] with 20s timeout...
[2025-07-15 07:11:11.388] [info] Pipeline statistics reset
[2025-07-15 07:11:11.388] [info] X86_64CPU[4] initialized with full register support. RIP=0x1000
[2025-07-15 07:11:11.398] [info] CPU[4] initialized successfully in 10ms
[2025-07-15 07:11:11.399] [info] Initializing CPU[5] with 20s timeout...
[2025-07-15 07:11:11.399] [info] Pipeline statistics reset
[2025-07-15 07:11:11.399] [info] X86_64CPU[5] initialized with full register support. RIP=0x1000
[2025-07-15 07:11:11.409] [info] CPU[5] initialized successfully in 10ms
[2025-07-15 07:11:11.409] [info] Initializing CPU[6] with 20s timeout...
[2025-07-15 07:11:11.409] [info] Pipeline statistics reset
[2025-07-15 07:11:11.409] [info] X86_64CPU[6] initialized with full register support. RIP=0x1000
[2025-07-15 07:11:11.419] [info] CPU[6] initialized successfully in 10ms
[2025-07-15 07:11:11.420] [info] Initializing CPU[7] with 20s timeout...
[2025-07-15 07:11:11.420] [info] Pipeline statistics reset
[2025-07-15 07:11:11.420] [info] X86_64CPU[7] initialized with full register support. RIP=0x1000
[2025-07-15 07:11:11.430] [info] CPU[7] initialized successfully in 10ms
[2025-07-15 07:11:11.430] [info] Step 7/10: Initializing interrupt handler...
[2025-07-15 07:11:11.430] [info] Initializing InterruptHandler with 10s timeout...
[2025-07-15 07:11:11.430] [info] Initializing InterruptHandler...
[2025-07-15 07:11:11.430] [info] Allocating IDT memory: size=0x1000
[2025-07-15 07:11:11.430] [info] Allocating virtual memory: process=0, size=0x1000
[2025-07-15 07:11:11.431] [info] Searching for free virtual range: size=0x1000, alignment=0x1000
[2025-07-15 07:11:11.431] [info] Found free virtual range at 0x10000 (beyond page table)
[2025-07-15 07:11:11.431] [info] AllocateVirtual: Allocating 1 pages starting at 0x10000
[2025-07-15 07:11:11.431] [debug] Resizing page table to accommodate index 16
[2025-07-15 07:11:11.431] [debug] AllocatePage: Allocated phys=0x1000 in 0ms
[2025-07-15 07:11:11.431] [debug] AllocatePage: TLB inserted: virt=0x10000, phys=0x1000
[2025-07-15 07:11:11.431] [info] Allocated page: virt=0x10000, phys=0x1000
[2025-07-15 07:11:11.431] [info] Allocated virtual memory: addr=0x10000, size=0x1000, process=0
[2025-07-15 07:11:11.431] [info] IDT allocated at 0x10000
[2025-07-15 07:11:11.431] [info] Zeroing IDT memory
[2025-07-15 07:11:11.431] [debug] Writing virtual memory chunk: virt=0x10000, size=0x1000, process=0
[2025-07-15 07:11:11.431] [info] Translating virtual to physical: virt=0x10000, process=0
[2025-07-15 07:11:11.431] [debug] Marked page dirty: virt=0x10000
[2025-07-15 07:11:11.431] [debug] Wrote virtual memory chunk: virt=0x10000, size=0x1000
[2025-07-15 07:11:11.431] [info] IDT memory zeroed
[2025-07-15 07:11:11.431] [info] Allocating handler stubs: entry_count=256, size=0x1000
[2025-07-15 07:11:11.432] [info] Allocating virtual memory: process=0, size=0x1000
[2025-07-15 07:11:11.432] [info] Searching for free virtual range: size=0x1000, alignment=0x1000
[2025-07-15 07:11:11.432] [info] Found free virtual range at 0x11000 (beyond page table)
[2025-07-15 07:11:11.432] [info] AllocateVirtual: Allocating 1 pages starting at 0x11000
[2025-07-15 07:11:11.432] [debug] Resizing page table to accommodate index 17
[2025-07-15 07:11:11.432] [debug] AllocatePage: Allocated phys=0x2000 in 0ms
[2025-07-15 07:11:11.432] [debug] AllocatePage: TLB inserted: virt=0x11000, phys=0x2000
[2025-07-15 07:11:11.432] [info] Allocated page: virt=0x11000, phys=0x2000
[2025-07-15 07:11:11.432] [info] Allocated virtual memory: addr=0x11000, size=0x1000, process=0
[2025-07-15 07:11:11.432] [info] Handler stubs allocated at 0x11000
[2025-07-15 07:11:11.432] [info] Setting up IDT descriptors
[2025-07-15 07:11:11.432] [debug] Writing virtual memory chunk: virt=0x11000, size=0x1, process=0
[2025-07-15 07:11:11.432] [info] Translating virtual to physical: virt=0x11000, process=0
[2025-07-15 07:11:11.432] [debug] Marked page dirty: virt=0x11000
[2025-07-15 07:11:11.433] [debug] Wrote virtual memory chunk: virt=0x11000, size=0x1
[2025-07-15 07:11:11.434] [debug] Writing virtual memory chunk: virt=0x10000, size=0x10, process=0
[2025-07-15 07:11:11.434] [info] Translating virtual to physical: virt=0x10000, process=0
[2025-07-15 07:11:11.434] [debug] Marked page dirty: virt=0x10000
[2025-07-15 07:11:11.434] [debug] Wrote virtual memory chunk: virt=0x10000, size=0x10
[2025-07-15 07:11:11.434] [info] Processed descriptor 0
[2025-07-15 07:11:11.434] [debug] Writing virtual memory chunk: virt=0x11010, size=0x1, process=0
[2025-07-15 07:11:11.435] [info] Translating virtual to physical: virt=0x11010, process=0
[2025-07-15 07:11:11.435] [debug] Marked page dirty: virt=0x11010
[2025-07-15 07:11:11.435] [debug] Wrote virtual memory chunk: virt=0x11010, size=0x1
[2025-07-15 07:11:11.435] [debug] Writing virtual memory chunk: virt=0x10010, size=0x10, process=0
[2025-07-15 07:11:11.435] [info] Translating virtual to physical: virt=0x10010, process=0
[2025-07-15 07:11:11.435] [debug] Marked page dirty: virt=0x10010
[2025-07-15 07:11:11.435] [debug] Wrote virtual memory chunk: virt=0x10010, size=0x10
[2025-07-15 07:11:11.435] [debug] Writing virtual memory chunk: virt=0x11020, size=0x1, process=0
[2025-07-15 07:11:11.435] [info] Translating virtual to physical: virt=0x11020, process=0
[2025-07-15 07:11:11.435] [debug] Marked page dirty: virt=0x11020
[2025-07-15 07:11:11.435] [debug] Wrote virtual memory chunk: virt=0x11020, size=0x1
[2025-07-15 07:11:11.435] [debug] Writing virtual memory chunk: virt=0x10020, size=0x10, process=0
[2025-07-15 07:11:11.435] [info] Translating virtual to physical: virt=0x10020, process=0
[2025-07-15 07:11:11.435] [debug] Marked page dirty: virt=0x10020
[2025-07-15 07:11:11.435] [debug] Wrote virtual memory chunk: virt=0x10020, size=0x10
[2025-07-15 07:11:11.435] [debug] Writing virtual memory chunk: virt=0x11030, size=0x1, process=0
[2025-07-15 07:11:11.435] [info] Translating virtual to physical: virt=0x11030, process=0
[2025-07-15 07:11:11.435] [debug] Marked page dirty: virt=0x11030
[2025-07-15 07:11:11.436] [debug] Wrote virtual memory chunk: virt=0x11030, size=0x1
[2025-07-15 07:11:11.436] [debug] Writing virtual memory chunk: virt=0x10030, size=0x10, process=0
[2025-07-15 07:11:11.436] [info] Translating virtual to physical: virt=0x10030, process=0
[2025-07-15 07:11:11.436] [debug] Marked page dirty: virt=0x10030
[2025-07-15 07:11:11.436] [debug] Wrote virtual memory chunk: virt=0x10030, size=0x10
[2025-07-15 07:11:11.436] [debug] Writing virtual memory chunk: virt=0x11040, size=0x1, process=0
[2025-07-15 07:11:11.436] [info] Translating virtual to physical: virt=0x11040, process=0
[2025-07-15 07:11:11.436] [debug] Marked page dirty: virt=0x11040
[2025-07-15 07:11:11.436] [debug] Wrote virtual memory chunk: virt=0x11040, size=0x1
[2025-07-15 07:11:11.436] [debug] Writing virtual memory chunk: virt=0x10040, size=0x10, process=0
[2025-07-15 07:11:11.436] [info] Translating virtual to physical: virt=0x10040, process=0
[2025-07-15 07:11:11.436] [debug] Marked page dirty: virt=0x10040
[2025-07-15 07:11:11.436] [debug] Wrote virtual memory chunk: virt=0x10040, size=0x10
[2025-07-15 07:11:11.437] [debug] Writing virtual memory chunk: virt=0x11050, size=0x1, process=0
[2025-07-15 07:11:11.437] [info] Translating virtual to physical: virt=0x11050, process=0
[2025-07-15 07:11:11.437] [debug] Marked page dirty: virt=0x11050
[2025-07-15 07:11:11.437] [debug] Wrote virtual memory chunk: virt=0x11050, size=0x1
[2025-07-15 07:11:11.437] [debug] Writing virtual memory chunk: virt=0x10050, size=0x10, process=0
[2025-07-15 07:11:11.437] [info] Translating virtual to physical: virt=0x10050, process=0
[2025-07-15 07:11:11.437] [debug] Marked page dirty: virt=0x10050
[2025-07-15 07:11:11.437] [debug] Wrote virtual memory chunk: virt=0x10050, size=0x10
[2025-07-15 07:11:11.437] [debug] Writing virtual memory chunk: virt=0x11060, size=0x1, process=0
[2025-07-15 07:11:11.437] [info] Translating virtual to physical: virt=0x11060, process=0
[2025-07-15 07:11:11.437] [debug] Marked page dirty: virt=0x11060
[2025-07-15 07:11:11.437] [debug] Wrote virtual memory chunk: virt=0x11060, size=0x1
[2025-07-15 07:11:11.437] [debug] Writing virtual memory chunk: virt=0x10060, size=0x10, process=0
[2025-07-15 07:11:11.437] [info] Translating virtual to physical: virt=0x10060, process=0
[2025-07-15 07:11:11.438] [debug] Marked page dirty: virt=0x10060
[2025-07-15 07:11:11.438] [debug] Wrote virtual memory chunk: virt=0x10060, size=0x10
[2025-07-15 07:11:11.438] [debug] Writing virtual memory chunk: virt=0x11070, size=0x1, process=0
[2025-07-15 07:11:11.438] [info] Translating virtual to physical: virt=0x11070, process=0
[2025-07-15 07:11:11.438] [debug] Marked page dirty: virt=0x11070
[2025-07-15 07:11:11.438] [debug] Wrote virtual memory chunk: virt=0x11070, size=0x1
[2025-07-15 07:11:11.438] [debug] Writing virtual memory chunk: virt=0x10070, size=0x10, process=0
[2025-07-15 07:11:11.438] [info] Translating virtual to physical: virt=0x10070, process=0
[2025-07-15 07:11:11.438] [debug] Marked page dirty: virt=0x10070
[2025-07-15 07:11:11.438] [debug] Wrote virtual memory chunk: virt=0x10070, size=0x10
[2025-07-15 07:11:11.438] [debug] Writing virtual memory chunk: virt=0x11080, size=0x1, process=0
[2025-07-15 07:11:11.438] [info] Translating virtual to physical: virt=0x11080, process=0
[2025-07-15 07:11:11.438] [debug] Marked page dirty: virt=0x11080
[2025-07-15 07:11:11.439] [debug] Wrote virtual memory chunk: virt=0x11080, size=0x1
[2025-07-15 07:11:11.439] [debug] Writing virtual memory chunk: virt=0x10080, size=0x10, process=0
[2025-07-15 07:11:11.439] [info] Translating virtual to physical: virt=0x10080, process=0
[2025-07-15 07:11:11.439] [debug] Marked page dirty: virt=0x10080
[2025-07-15 07:11:11.439] [debug] Wrote virtual memory chunk: virt=0x10080, size=0x10
[2025-07-15 07:11:11.439] [debug] Writing virtual memory chunk: virt=0x11090, size=0x1, process=0
[2025-07-15 07:11:11.439] [info] Translating virtual to physical: virt=0x11090, process=0
[2025-07-15 07:11:11.439] [debug] Marked page dirty: virt=0x11090
[2025-07-15 07:11:11.439] [debug] Wrote virtual memory chunk: virt=0x11090, size=0x1
[2025-07-15 07:11:11.439] [debug] Writing virtual memory chunk: virt=0x10090, size=0x10, process=0
[2025-07-15 07:11:11.439] [info] Translating virtual to physical: virt=0x10090, process=0
[2025-07-15 07:11:11.439] [debug] Marked page dirty: virt=0x10090
[2025-07-15 07:11:11.439] [debug] Wrote virtual memory chunk: virt=0x10090, size=0x10
[2025-07-15 07:11:11.440] [debug] Writing virtual memory chunk: virt=0x110a0, size=0x1, process=0
[2025-07-15 07:11:11.440] [info] Translating virtual to physical: virt=0x110a0, process=0
[2025-07-15 07:11:11.440] [debug] Marked page dirty: virt=0x110a0
[2025-07-15 07:11:11.440] [debug] Wrote virtual memory chunk: virt=0x110a0, size=0x1
[2025-07-15 07:11:11.440] [debug] Writing virtual memory chunk: virt=0x100a0, size=0x10, process=0
[2025-07-15 07:11:11.440] [info] Translating virtual to physical: virt=0x100a0, process=0
[2025-07-15 07:11:11.440] [debug] Marked page dirty: virt=0x100a0
[2025-07-15 07:11:11.440] [debug] Wrote virtual memory chunk: virt=0x100a0, size=0x10
[2025-07-15 07:11:11.440] [debug] Writing virtual memory chunk: virt=0x110b0, size=0x1, process=0
[2025-07-15 07:11:11.440] [info] Translating virtual to physical: virt=0x110b0, process=0
[2025-07-15 07:11:11.440] [debug] Marked page dirty: virt=0x110b0
[2025-07-15 07:11:11.440] [debug] Wrote virtual memory chunk: virt=0x110b0, size=0x1
[2025-07-15 07:11:11.440] [debug] Writing virtual memory chunk: virt=0x100b0, size=0x10, process=0
[2025-07-15 07:11:11.440] [info] Translating virtual to physical: virt=0x100b0, process=0
[2025-07-15 07:11:11.440] [debug] Marked page dirty: virt=0x100b0
[2025-07-15 07:11:11.440] [debug] Wrote virtual memory chunk: virt=0x100b0, size=0x10
[2025-07-15 07:11:11.441] [debug] Writing virtual memory chunk: virt=0x110c0, size=0x1, process=0
[2025-07-15 07:11:11.441] [info] Translating virtual to physical: virt=0x110c0, process=0
[2025-07-15 07:11:11.441] [debug] Marked page dirty: virt=0x110c0
[2025-07-15 07:11:11.441] [debug] Wrote virtual memory chunk: virt=0x110c0, size=0x1
[2025-07-15 07:11:11.441] [debug] Writing virtual memory chunk: virt=0x100c0, size=0x10, process=0
[2025-07-15 07:11:11.441] [info] Translating virtual to physical: virt=0x100c0, process=0
[2025-07-15 07:11:11.441] [debug] Marked page dirty: virt=0x100c0
[2025-07-15 07:11:11.441] [debug] Wrote virtual memory chunk: virt=0x100c0, size=0x10
[2025-07-15 07:11:11.441] [debug] Writing virtual memory chunk: virt=0x110d0, size=0x1, process=0
[2025-07-15 07:11:11.441] [info] Translating virtual to physical: virt=0x110d0, process=0
[2025-07-15 07:11:11.441] [debug] Marked page dirty: virt=0x110d0
[2025-07-15 07:11:11.441] [debug] Wrote virtual memory chunk: virt=0x110d0, size=0x1
[2025-07-15 07:11:11.441] [debug] Writing virtual memory chunk: virt=0x100d0, size=0x10, process=0
[2025-07-15 07:11:11.441] [info] Translating virtual to physical: virt=0x100d0, process=0
[2025-07-15 07:11:11.442] [debug] Marked page dirty: virt=0x100d0
[2025-07-15 07:11:11.442] [debug] Wrote virtual memory chunk: virt=0x100d0, size=0x10
[2025-07-15 07:11:11.442] [debug] Writing virtual memory chunk: virt=0x110e0, size=0x1, process=0
[2025-07-15 07:11:11.442] [info] Translating virtual to physical: virt=0x110e0, process=0
[2025-07-15 07:11:11.442] [debug] Marked page dirty: virt=0x110e0
[2025-07-15 07:11:11.442] [debug] Wrote virtual memory chunk: virt=0x110e0, size=0x1
[2025-07-15 07:11:11.442] [debug] Writing virtual memory chunk: virt=0x100e0, size=0x10, process=0
[2025-07-15 07:11:11.442] [info] Translating virtual to physical: virt=0x100e0, process=0
[2025-07-15 07:11:11.442] [debug] Marked page dirty: virt=0x100e0
[2025-07-15 07:11:11.442] [debug] Wrote virtual memory chunk: virt=0x100e0, size=0x10
[2025-07-15 07:11:11.442] [debug] Writing virtual memory chunk: virt=0x110f0, size=0x1, process=0
[2025-07-15 07:11:11.442] [info] Translating virtual to physical: virt=0x110f0, process=0
[2025-07-15 07:11:11.442] [debug] Marked page dirty: virt=0x110f0
[2025-07-15 07:11:11.442] [debug] Wrote virtual memory chunk: virt=0x110f0, size=0x1
[2025-07-15 07:11:11.442] [debug] Writing virtual memory chunk: virt=0x100f0, size=0x10, process=0
[2025-07-15 07:11:11.442] [info] Translating virtual to physical: virt=0x100f0, process=0
[2025-07-15 07:11:11.443] [debug] Marked page dirty: virt=0x100f0
[2025-07-15 07:11:11.443] [debug] Wrote virtual memory chunk: virt=0x100f0, size=0x10
[2025-07-15 07:11:11.443] [debug] Writing virtual memory chunk: virt=0x11100, size=0x1, process=0
[2025-07-15 07:11:11.443] [info] Translating virtual to physical: virt=0x11100, process=0
[2025-07-15 07:11:11.443] [debug] Marked page dirty: virt=0x11100
[2025-07-15 07:11:11.443] [debug] Wrote virtual memory chunk: virt=0x11100, size=0x1
[2025-07-15 07:11:11.443] [debug] Writing virtual memory chunk: virt=0x10100, size=0x10, process=0
[2025-07-15 07:11:11.443] [info] Translating virtual to physical: virt=0x10100, process=0
[2025-07-15 07:11:11.443] [debug] Marked page dirty: virt=0x10100
[2025-07-15 07:11:11.443] [debug] Wrote virtual memory chunk: virt=0x10100, size=0x10
[2025-07-15 07:11:11.443] [debug] Writing virtual memory chunk: virt=0x11110, size=0x1, process=0
[2025-07-15 07:11:11.443] [info] Translating virtual to physical: virt=0x11110, process=0
[2025-07-15 07:11:11.443] [debug] Marked page dirty: virt=0x11110
[2025-07-15 07:11:11.443] [debug] Wrote virtual memory chunk: virt=0x11110, size=0x1
[2025-07-15 07:11:11.443] [debug] Writing virtual memory chunk: virt=0x10110, size=0x10, process=0
[2025-07-15 07:11:11.443] [info] Translating virtual to physical: virt=0x10110, process=0
[2025-07-15 07:11:11.444] [debug] Marked page dirty: virt=0x10110
[2025-07-15 07:11:11.444] [debug] Wrote virtual memory chunk: virt=0x10110, size=0x10
[2025-07-15 07:11:11.444] [debug] Writing virtual memory chunk: virt=0x11120, size=0x1, process=0
[2025-07-15 07:11:11.444] [info] Translating virtual to physical: virt=0x11120, process=0
[2025-07-15 07:11:11.444] [debug] Marked page dirty: virt=0x11120
[2025-07-15 07:11:11.444] [debug] Wrote virtual memory chunk: virt=0x11120, size=0x1
[2025-07-15 07:11:11.444] [debug] Writing virtual memory chunk: virt=0x10120, size=0x10, process=0
[2025-07-15 07:11:11.444] [info] Translating virtual to physical: virt=0x10120, process=0
[2025-07-15 07:11:11.444] [debug] Marked page dirty: virt=0x10120
[2025-07-15 07:11:11.444] [debug] Wrote virtual memory chunk: virt=0x10120, size=0x10
[2025-07-15 07:11:11.444] [debug] Writing virtual memory chunk: virt=0x11130, size=0x1, process=0
[2025-07-15 07:11:11.444] [info] Translating virtual to physical: virt=0x11130, process=0
[2025-07-15 07:11:11.444] [debug] Marked page dirty: virt=0x11130
[2025-07-15 07:11:11.444] [debug] Wrote virtual memory chunk: virt=0x11130, size=0x1
[2025-07-15 07:11:11.445] [debug] Writing virtual memory chunk: virt=0x10130, size=0x10, process=0
[2025-07-15 07:11:11.445] [info] Translating virtual to physical: virt=0x10130, process=0
[2025-07-15 07:11:11.445] [debug] Marked page dirty: virt=0x10130
[2025-07-15 07:11:11.445] [debug] Wrote virtual memory chunk: virt=0x10130, size=0x10
[2025-07-15 07:11:11.445] [debug] Writing virtual memory chunk: virt=0x11140, size=0x1, process=0
[2025-07-15 07:11:11.445] [info] Translating virtual to physical: virt=0x11140, process=0
[2025-07-15 07:11:11.445] [debug] Marked page dirty: virt=0x11140
[2025-07-15 07:11:11.445] [debug] Wrote virtual memory chunk: virt=0x11140, size=0x1
[2025-07-15 07:11:11.445] [debug] Writing virtual memory chunk: virt=0x10140, size=0x10, process=0
[2025-07-15 07:11:11.445] [info] Translating virtual to physical: virt=0x10140, process=0
[2025-07-15 07:11:11.445] [debug] Marked page dirty: virt=0x10140
[2025-07-15 07:11:11.445] [debug] Wrote virtual memory chunk: virt=0x10140, size=0x10
[2025-07-15 07:11:11.445] [debug] Writing virtual memory chunk: virt=0x11150, size=0x1, process=0
[2025-07-15 07:11:11.445] [info] Translating virtual to physical: virt=0x11150, process=0
[2025-07-15 07:11:11.445] [debug] Marked page dirty: virt=0x11150
[2025-07-15 07:11:11.445] [debug] Wrote virtual memory chunk: virt=0x11150, size=0x1
[2025-07-15 07:11:11.446] [debug] Writing virtual memory chunk: virt=0x10150, size=0x10, process=0
[2025-07-15 07:11:11.446] [info] Translating virtual to physical: virt=0x10150, process=0
[2025-07-15 07:11:11.446] [debug] Marked page dirty: virt=0x10150
[2025-07-15 07:11:11.446] [debug] Wrote virtual memory chunk: virt=0x10150, size=0x10
[2025-07-15 07:11:11.446] [debug] Writing virtual memory chunk: virt=0x11160, size=0x1, process=0
[2025-07-15 07:11:11.446] [info] Translating virtual to physical: virt=0x11160, process=0
[2025-07-15 07:11:11.446] [debug] Marked page dirty: virt=0x11160
[2025-07-15 07:11:11.446] [debug] Wrote virtual memory chunk: virt=0x11160, size=0x1
[2025-07-15 07:11:11.446] [debug] Writing virtual memory chunk: virt=0x10160, size=0x10, process=0
[2025-07-15 07:11:11.446] [info] Translating virtual to physical: virt=0x10160, process=0
[2025-07-15 07:11:11.446] [debug] Marked page dirty: virt=0x10160
[2025-07-15 07:11:11.446] [debug] Wrote virtual memory chunk: virt=0x10160, size=0x10
[2025-07-15 07:11:11.446] [debug] Writing virtual memory chunk: virt=0x11170, size=0x1, process=0
[2025-07-15 07:11:11.446] [info] Translating virtual to physical: virt=0x11170, process=0
[2025-07-15 07:11:11.446] [debug] Marked page dirty: virt=0x11170
[2025-07-15 07:11:11.447] [debug] Wrote virtual memory chunk: virt=0x11170, size=0x1
[2025-07-15 07:11:11.447] [debug] Writing virtual memory chunk: virt=0x10170, size=0x10, process=0
[2025-07-15 07:11:11.447] [info] Translating virtual to physical: virt=0x10170, process=0
[2025-07-15 07:11:11.447] [debug] Marked page dirty: virt=0x10170
[2025-07-15 07:11:11.447] [debug] Wrote virtual memory chunk: virt=0x10170, size=0x10
[2025-07-15 07:11:11.447] [debug] Writing virtual memory chunk: virt=0x11180, size=0x1, process=0
[2025-07-15 07:11:11.447] [info] Translating virtual to physical: virt=0x11180, process=0
[2025-07-15 07:11:11.447] [debug] Marked page dirty: virt=0x11180
[2025-07-15 07:11:11.447] [debug] Wrote virtual memory chunk: virt=0x11180, size=0x1
[2025-07-15 07:11:11.447] [debug] Writing virtual memory chunk: virt=0x10180, size=0x10, process=0
[2025-07-15 07:11:11.447] [info] Translating virtual to physical: virt=0x10180, process=0
[2025-07-15 07:11:11.447] [debug] Marked page dirty: virt=0x10180
[2025-07-15 07:11:11.447] [debug] Wrote virtual memory chunk: virt=0x10180, size=0x10
[2025-07-15 07:11:11.447] [debug] Writing virtual memory chunk: virt=0x11190, size=0x1, process=0
[2025-07-15 07:11:11.448] [info] Translating virtual to physical: virt=0x11190, process=0
[2025-07-15 07:11:11.448] [debug] Marked page dirty: virt=0x11190
[2025-07-15 07:11:11.448] [debug] Wrote virtual memory chunk: virt=0x11190, size=0x1
[2025-07-15 07:11:11.448] [debug] Writing virtual memory chunk: virt=0x10190, size=0x10, process=0
[2025-07-15 07:11:11.448] [info] Translating virtual to physical: virt=0x10190, process=0
[2025-07-15 07:11:11.448] [debug] Marked page dirty: virt=0x10190
[2025-07-15 07:11:11.450] [debug] Wrote virtual memory chunk: virt=0x10190, size=0x10
[2025-07-15 07:11:11.450] [debug] Writing virtual memory chunk: virt=0x111a0, size=0x1, process=0
[2025-07-15 07:11:11.450] [info] Translating virtual to physical: virt=0x111a0, process=0
[2025-07-15 07:11:11.450] [debug] Marked page dirty: virt=0x111a0
[2025-07-15 07:11:11.450] [debug] Wrote virtual memory chunk: virt=0x111a0, size=0x1
[2025-07-15 07:11:11.450] [debug] Writing virtual memory chunk: virt=0x101a0, size=0x10, process=0
[2025-07-15 07:11:11.450] [info] Translating virtual to physical: virt=0x101a0, process=0
[2025-07-15 07:11:11.450] [debug] Marked page dirty: virt=0x101a0
[2025-07-15 07:11:11.450] [debug] Wrote virtual memory chunk: virt=0x101a0, size=0x10
[2025-07-15 07:11:11.450] [debug] Writing virtual memory chunk: virt=0x111b0, size=0x1, process=0
[2025-07-15 07:11:11.450] [info] Translating virtual to physical: virt=0x111b0, process=0
[2025-07-15 07:11:11.450] [debug] Marked page dirty: virt=0x111b0
[2025-07-15 07:11:11.451] [debug] Wrote virtual memory chunk: virt=0x111b0, size=0x1
[2025-07-15 07:11:11.451] [debug] Writing virtual memory chunk: virt=0x101b0, size=0x10, process=0
[2025-07-15 07:11:11.451] [info] Translating virtual to physical: virt=0x101b0, process=0
[2025-07-15 07:11:11.451] [debug] Marked page dirty: virt=0x101b0
[2025-07-15 07:11:11.451] [debug] Wrote virtual memory chunk: virt=0x101b0, size=0x10
[2025-07-15 07:11:11.451] [debug] Writing virtual memory chunk: virt=0x111c0, size=0x1, process=0
[2025-07-15 07:11:11.451] [info] Translating virtual to physical: virt=0x111c0, process=0
[2025-07-15 07:11:11.451] [debug] Marked page dirty: virt=0x111c0
[2025-07-15 07:11:11.451] [debug] Wrote virtual memory chunk: virt=0x111c0, size=0x1
[2025-07-15 07:11:11.451] [debug] Writing virtual memory chunk: virt=0x101c0, size=0x10, process=0
[2025-07-15 07:11:11.451] [info] Translating virtual to physical: virt=0x101c0, process=0
[2025-07-15 07:11:11.451] [debug] Marked page dirty: virt=0x101c0
[2025-07-15 07:11:11.451] [debug] Wrote virtual memory chunk: virt=0x101c0, size=0x10
[2025-07-15 07:11:11.451] [debug] Writing virtual memory chunk: virt=0x111d0, size=0x1, process=0
[2025-07-15 07:11:11.451] [info] Translating virtual to physical: virt=0x111d0, process=0
[2025-07-15 07:11:11.451] [debug] Marked page dirty: virt=0x111d0
[2025-07-15 07:11:11.452] [debug] Wrote virtual memory chunk: virt=0x111d0, size=0x1
[2025-07-15 07:11:11.452] [debug] Writing virtual memory chunk: virt=0x101d0, size=0x10, process=0
[2025-07-15 07:11:11.452] [info] Translating virtual to physical: virt=0x101d0, process=0
[2025-07-15 07:11:11.452] [debug] Marked page dirty: virt=0x101d0
[2025-07-15 07:11:11.452] [debug] Wrote virtual memory chunk: virt=0x101d0, size=0x10
[2025-07-15 07:11:11.452] [debug] Writing virtual memory chunk: virt=0x111e0, size=0x1, process=0
[2025-07-15 07:11:11.452] [info] Translating virtual to physical: virt=0x111e0, process=0
[2025-07-15 07:11:11.452] [debug] Marked page dirty: virt=0x111e0
[2025-07-15 07:11:11.452] [debug] Wrote virtual memory chunk: virt=0x111e0, size=0x1
[2025-07-15 07:11:11.452] [debug] Writing virtual memory chunk: virt=0x101e0, size=0x10, process=0
[2025-07-15 07:11:11.452] [info] Translating virtual to physical: virt=0x101e0, process=0
[2025-07-15 07:11:11.452] [debug] Marked page dirty: virt=0x101e0
[2025-07-15 07:11:11.452] [debug] Wrote virtual memory chunk: virt=0x101e0, size=0x10
[2025-07-15 07:11:11.452] [debug] Writing virtual memory chunk: virt=0x111f0, size=0x1, process=0
[2025-07-15 07:11:11.452] [info] Translating virtual to physical: virt=0x111f0, process=0
[2025-07-15 07:11:11.453] [debug] Marked page dirty: virt=0x111f0
[2025-07-15 07:11:11.453] [debug] Wrote virtual memory chunk: virt=0x111f0, size=0x1
[2025-07-15 07:11:11.453] [debug] Writing virtual memory chunk: virt=0x101f0, size=0x10, process=0
[2025-07-15 07:11:11.453] [info] Translating virtual to physical: virt=0x101f0, process=0
[2025-07-15 07:11:11.453] [debug] Marked page dirty: virt=0x101f0
[2025-07-15 07:11:11.453] [debug] Wrote virtual memory chunk: virt=0x101f0, size=0x10
[2025-07-15 07:11:11.453] [debug] Writing virtual memory chunk: virt=0x11200, size=0x1, process=0
[2025-07-15 07:11:11.453] [info] Translating virtual to physical: virt=0x11200, process=0
[2025-07-15 07:11:11.453] [debug] Marked page dirty: virt=0x11200
[2025-07-15 07:11:11.453] [debug] Wrote virtual memory chunk: virt=0x11200, size=0x1
[2025-07-15 07:11:11.453] [debug] Writing virtual memory chunk: virt=0x10200, size=0x10, process=0
[2025-07-15 07:11:11.453] [info] Translating virtual to physical: virt=0x10200, process=0
[2025-07-15 07:11:11.453] [debug] Marked page dirty: virt=0x10200
[2025-07-15 07:11:11.453] [debug] Wrote virtual memory chunk: virt=0x10200, size=0x10
[2025-07-15 07:11:11.453] [debug] Writing virtual memory chunk: virt=0x11210, size=0x1, process=0
[2025-07-15 07:11:11.453] [info] Translating virtual to physical: virt=0x11210, process=0
[2025-07-15 07:11:11.454] [debug] Marked page dirty: virt=0x11210
[2025-07-15 07:11:11.454] [debug] Wrote virtual memory chunk: virt=0x11210, size=0x1
[2025-07-15 07:11:11.454] [debug] Writing virtual memory chunk: virt=0x10210, size=0x10, process=0
[2025-07-15 07:11:11.454] [info] Translating virtual to physical: virt=0x10210, process=0
[2025-07-15 07:11:11.454] [debug] Marked page dirty: virt=0x10210
[2025-07-15 07:11:11.454] [debug] Wrote virtual memory chunk: virt=0x10210, size=0x10
[2025-07-15 07:11:11.454] [debug] Writing virtual memory chunk: virt=0x11220, size=0x1, process=0
[2025-07-15 07:11:11.454] [info] Translating virtual to physical: virt=0x11220, process=0
[2025-07-15 07:11:11.454] [debug] Marked page dirty: virt=0x11220
[2025-07-15 07:11:11.454] [debug] Wrote virtual memory chunk: virt=0x11220, size=0x1
[2025-07-15 07:11:11.454] [debug] Writing virtual memory chunk: virt=0x10220, size=0x10, process=0
[2025-07-15 07:11:11.454] [info] Translating virtual to physical: virt=0x10220, process=0
[2025-07-15 07:11:11.454] [debug] Marked page dirty: virt=0x10220
[2025-07-15 07:11:11.454] [debug] Wrote virtual memory chunk: virt=0x10220, size=0x10
[2025-07-15 07:11:11.455] [debug] Writing virtual memory chunk: virt=0x11230, size=0x1, process=0
[2025-07-15 07:11:11.455] [info] Translating virtual to physical: virt=0x11230, process=0
[2025-07-15 07:11:11.455] [debug] Marked page dirty: virt=0x11230
[2025-07-15 07:11:11.455] [debug] Wrote virtual memory chunk: virt=0x11230, size=0x1
[2025-07-15 07:11:11.455] [debug] Writing virtual memory chunk: virt=0x10230, size=0x10, process=0
[2025-07-15 07:11:11.455] [info] Translating virtual to physical: virt=0x10230, process=0
[2025-07-15 07:11:11.455] [debug] Marked page dirty: virt=0x10230
[2025-07-15 07:11:11.455] [debug] Wrote virtual memory chunk: virt=0x10230, size=0x10
[2025-07-15 07:11:11.455] [debug] Writing virtual memory chunk: virt=0x11240, size=0x1, process=0
[2025-07-15 07:11:11.455] [info] Translating virtual to physical: virt=0x11240, process=0
[2025-07-15 07:11:11.455] [debug] Marked page dirty: virt=0x11240
[2025-07-15 07:11:11.455] [debug] Wrote virtual memory chunk: virt=0x11240, size=0x1
[2025-07-15 07:11:11.455] [debug] Writing virtual memory chunk: virt=0x10240, size=0x10, process=0
[2025-07-15 07:11:11.455] [info] Translating virtual to physical: virt=0x10240, process=0
[2025-07-15 07:11:11.456] [debug] Marked page dirty: virt=0x10240
[2025-07-15 07:11:11.456] [debug] Wrote virtual memory chunk: virt=0x10240, size=0x10
[2025-07-15 07:11:11.456] [debug] Writing virtual memory chunk: virt=0x11250, size=0x1, process=0
[2025-07-15 07:11:11.456] [info] Translating virtual to physical: virt=0x11250, process=0
[2025-07-15 07:11:11.456] [debug] Marked page dirty: virt=0x11250
[2025-07-15 07:11:11.456] [debug] Wrote virtual memory chunk: virt=0x11250, size=0x1
[2025-07-15 07:11:11.456] [debug] Writing virtual memory chunk: virt=0x10250, size=0x10, process=0
[2025-07-15 07:11:11.456] [info] Translating virtual to physical: virt=0x10250, process=0
[2025-07-15 07:11:11.456] [debug] Marked page dirty: virt=0x10250
[2025-07-15 07:11:11.456] [debug] Wrote virtual memory chunk: virt=0x10250, size=0x10
[2025-07-15 07:11:11.456] [debug] Writing virtual memory chunk: virt=0x11260, size=0x1, process=0
[2025-07-15 07:11:11.456] [info] Translating virtual to physical: virt=0x11260, process=0
[2025-07-15 07:11:11.456] [debug] Marked page dirty: virt=0x11260
[2025-07-15 07:11:11.457] [debug] Wrote virtual memory chunk: virt=0x11260, size=0x1
[2025-07-15 07:11:11.457] [debug] Writing virtual memory chunk: virt=0x10260, size=0x10, process=0
[2025-07-15 07:11:11.457] [info] Translating virtual to physical: virt=0x10260, process=0
[2025-07-15 07:11:11.457] [debug] Marked page dirty: virt=0x10260
[2025-07-15 07:11:11.457] [debug] Wrote virtual memory chunk: virt=0x10260, size=0x10
[2025-07-15 07:11:11.457] [debug] Writing virtual memory chunk: virt=0x11270, size=0x1, process=0
[2025-07-15 07:11:11.457] [info] Translating virtual to physical: virt=0x11270, process=0
[2025-07-15 07:11:11.457] [debug] Marked page dirty: virt=0x11270
[2025-07-15 07:11:11.457] [debug] Wrote virtual memory chunk: virt=0x11270, size=0x1
[2025-07-15 07:11:11.457] [debug] Writing virtual memory chunk: virt=0x10270, size=0x10, process=0
[2025-07-15 07:11:11.457] [info] Translating virtual to physical: virt=0x10270, process=0
[2025-07-15 07:11:11.457] [debug] Marked page dirty: virt=0x10270
[2025-07-15 07:11:11.457] [debug] Wrote virtual memory chunk: virt=0x10270, size=0x10
[2025-07-15 07:11:11.457] [debug] Writing virtual memory chunk: virt=0x11280, size=0x1, process=0
[2025-07-15 07:11:11.457] [info] Translating virtual to physical: virt=0x11280, process=0
[2025-07-15 07:11:11.457] [debug] Marked page dirty: virt=0x11280
[2025-07-15 07:11:11.457] [debug] Wrote virtual memory chunk: virt=0x11280, size=0x1
[2025-07-15 07:11:11.458] [debug] Writing virtual memory chunk: virt=0x10280, size=0x10, process=0
[2025-07-15 07:11:11.458] [info] Translating virtual to physical: virt=0x10280, process=0
[2025-07-15 07:11:11.458] [debug] Marked page dirty: virt=0x10280
[2025-07-15 07:11:11.458] [debug] Wrote virtual memory chunk: virt=0x10280, size=0x10
[2025-07-15 07:11:11.458] [debug] Writing virtual memory chunk: virt=0x11290, size=0x1, process=0
[2025-07-15 07:11:11.458] [info] Translating virtual to physical: virt=0x11290, process=0
[2025-07-15 07:11:11.458] [debug] Marked page dirty: virt=0x11290
[2025-07-15 07:11:11.458] [debug] Wrote virtual memory chunk: virt=0x11290, size=0x1
[2025-07-15 07:11:11.458] [debug] Writing virtual memory chunk: virt=0x10290, size=0x10, process=0
[2025-07-15 07:11:11.458] [info] Translating virtual to physical: virt=0x10290, process=0
[2025-07-15 07:11:11.458] [debug] Marked page dirty: virt=0x10290
[2025-07-15 07:11:11.458] [debug] Wrote virtual memory chunk: virt=0x10290, size=0x10
[2025-07-15 07:11:11.458] [debug] Writing virtual memory chunk: virt=0x112a0, size=0x1, process=0
[2025-07-15 07:11:11.458] [info] Translating virtual to physical: virt=0x112a0, process=0
[2025-07-15 07:11:11.459] [debug] Marked page dirty: virt=0x112a0
[2025-07-15 07:11:11.459] [debug] Wrote virtual memory chunk: virt=0x112a0, size=0x1
[2025-07-15 07:11:11.459] [debug] Writing virtual memory chunk: virt=0x102a0, size=0x10, process=0
[2025-07-15 07:11:11.459] [info] Translating virtual to physical: virt=0x102a0, process=0
[2025-07-15 07:11:11.459] [debug] Marked page dirty: virt=0x102a0
[2025-07-15 07:11:11.459] [debug] Wrote virtual memory chunk: virt=0x102a0, size=0x10
[2025-07-15 07:11:11.459] [debug] Writing virtual memory chunk: virt=0x112b0, size=0x1, process=0
[2025-07-15 07:11:11.459] [info] Translating virtual to physical: virt=0x112b0, process=0
[2025-07-15 07:11:11.459] [debug] Marked page dirty: virt=0x112b0
[2025-07-15 07:11:11.459] [debug] Wrote virtual memory chunk: virt=0x112b0, size=0x1
[2025-07-15 07:11:11.459] [debug] Writing virtual memory chunk: virt=0x102b0, size=0x10, process=0
[2025-07-15 07:11:11.459] [info] Translating virtual to physical: virt=0x102b0, process=0
[2025-07-15 07:11:11.459] [debug] Marked page dirty: virt=0x102b0
[2025-07-15 07:11:11.459] [debug] Wrote virtual memory chunk: virt=0x102b0, size=0x10
[2025-07-15 07:11:11.459] [debug] Writing virtual memory chunk: virt=0x112c0, size=0x1, process=0
[2025-07-15 07:11:11.459] [info] Translating virtual to physical: virt=0x112c0, process=0
[2025-07-15 07:11:11.460] [debug] Marked page dirty: virt=0x112c0
[2025-07-15 07:11:11.460] [debug] Wrote virtual memory chunk: virt=0x112c0, size=0x1
[2025-07-15 07:11:11.460] [debug] Writing virtual memory chunk: virt=0x102c0, size=0x10, process=0
[2025-07-15 07:11:11.460] [info] Translating virtual to physical: virt=0x102c0, process=0
[2025-07-15 07:11:11.460] [debug] Marked page dirty: virt=0x102c0
[2025-07-15 07:11:11.460] [debug] Wrote virtual memory chunk: virt=0x102c0, size=0x10
[2025-07-15 07:11:11.460] [debug] Writing virtual memory chunk: virt=0x112d0, size=0x1, process=0
[2025-07-15 07:11:11.460] [info] Translating virtual to physical: virt=0x112d0, process=0
[2025-07-15 07:11:11.460] [debug] Marked page dirty: virt=0x112d0
[2025-07-15 07:11:11.460] [debug] Wrote virtual memory chunk: virt=0x112d0, size=0x1
[2025-07-15 07:11:11.460] [debug] Writing virtual memory chunk: virt=0x102d0, size=0x10, process=0
[2025-07-15 07:11:11.460] [info] Translating virtual to physical: virt=0x102d0, process=0
[2025-07-15 07:11:11.460] [debug] Marked page dirty: virt=0x102d0
[2025-07-15 07:11:11.460] [debug] Wrote virtual memory chunk: virt=0x102d0, size=0x10
[2025-07-15 07:11:11.460] [debug] Writing virtual memory chunk: virt=0x112e0, size=0x1, process=0
[2025-07-15 07:11:11.460] [info] Translating virtual to physical: virt=0x112e0, process=0
[2025-07-15 07:11:11.460] [debug] Marked page dirty: virt=0x112e0
[2025-07-15 07:11:11.461] [debug] Wrote virtual memory chunk: virt=0x112e0, size=0x1
[2025-07-15 07:11:11.461] [debug] Writing virtual memory chunk: virt=0x102e0, size=0x10, process=0
[2025-07-15 07:11:11.461] [info] Translating virtual to physical: virt=0x102e0, process=0
[2025-07-15 07:11:11.461] [debug] Marked page dirty: virt=0x102e0
[2025-07-15 07:11:11.461] [debug] Wrote virtual memory chunk: virt=0x102e0, size=0x10
[2025-07-15 07:11:11.461] [debug] Writing virtual memory chunk: virt=0x112f0, size=0x1, process=0
[2025-07-15 07:11:11.461] [info] Translating virtual to physical: virt=0x112f0, process=0
[2025-07-15 07:11:11.461] [debug] Marked page dirty: virt=0x112f0
[2025-07-15 07:11:11.461] [debug] Wrote virtual memory chunk: virt=0x112f0, size=0x1
[2025-07-15 07:11:11.461] [debug] Writing virtual memory chunk: virt=0x102f0, size=0x10, process=0
[2025-07-15 07:11:11.461] [info] Translating virtual to physical: virt=0x102f0, process=0
[2025-07-15 07:11:11.461] [debug] Marked page dirty: virt=0x102f0
[2025-07-15 07:11:11.461] [debug] Wrote virtual memory chunk: virt=0x102f0, size=0x10
[2025-07-15 07:11:11.461] [debug] Writing virtual memory chunk: virt=0x11300, size=0x1, process=0
[2025-07-15 07:11:11.461] [info] Translating virtual to physical: virt=0x11300, process=0
[2025-07-15 07:11:11.461] [debug] Marked page dirty: virt=0x11300
[2025-07-15 07:11:11.461] [debug] Wrote virtual memory chunk: virt=0x11300, size=0x1
[2025-07-15 07:11:11.462] [debug] Writing virtual memory chunk: virt=0x10300, size=0x10, process=0
[2025-07-15 07:11:11.462] [info] Translating virtual to physical: virt=0x10300, process=0
[2025-07-15 07:11:11.462] [debug] Marked page dirty: virt=0x10300
[2025-07-15 07:11:11.462] [debug] Wrote virtual memory chunk: virt=0x10300, size=0x10
[2025-07-15 07:11:11.462] [debug] Writing virtual memory chunk: virt=0x11310, size=0x1, process=0
[2025-07-15 07:11:11.462] [info] Translating virtual to physical: virt=0x11310, process=0
[2025-07-15 07:11:11.462] [debug] Marked page dirty: virt=0x11310
[2025-07-15 07:11:11.462] [debug] Wrote virtual memory chunk: virt=0x11310, size=0x1
[2025-07-15 07:11:11.462] [debug] Writing virtual memory chunk: virt=0x10310, size=0x10, process=0
[2025-07-15 07:11:11.462] [info] Translating virtual to physical: virt=0x10310, process=0
[2025-07-15 07:11:11.462] [debug] Marked page dirty: virt=0x10310
[2025-07-15 07:11:11.462] [debug] Wrote virtual memory chunk: virt=0x10310, size=0x10
[2025-07-15 07:11:11.462] [debug] Writing virtual memory chunk: virt=0x11320, size=0x1, process=0
[2025-07-15 07:11:11.462] [info] Translating virtual to physical: virt=0x11320, process=0
[2025-07-15 07:11:11.462] [debug] Marked page dirty: virt=0x11320
[2025-07-15 07:11:11.463] [debug] Wrote virtual memory chunk: virt=0x11320, size=0x1
[2025-07-15 07:11:11.463] [debug] Writing virtual memory chunk: virt=0x10320, size=0x10, process=0
[2025-07-15 07:11:11.463] [info] Translating virtual to physical: virt=0x10320, process=0
[2025-07-15 07:11:11.463] [debug] Marked page dirty: virt=0x10320
[2025-07-15 07:11:11.463] [debug] Wrote virtual memory chunk: virt=0x10320, size=0x10
[2025-07-15 07:11:11.463] [info] Processed descriptor 50
[2025-07-15 07:11:11.463] [debug] Writing virtual memory chunk: virt=0x11330, size=0x1, process=0
[2025-07-15 07:11:11.463] [info] Translating virtual to physical: virt=0x11330, process=0
[2025-07-15 07:11:11.463] [debug] Marked page dirty: virt=0x11330
[2025-07-15 07:11:11.463] [debug] Wrote virtual memory chunk: virt=0x11330, size=0x1
[2025-07-15 07:11:11.463] [debug] Writing virtual memory chunk: virt=0x10330, size=0x10, process=0
[2025-07-15 07:11:11.463] [info] Translating virtual to physical: virt=0x10330, process=0
[2025-07-15 07:11:11.463] [debug] Marked page dirty: virt=0x10330
[2025-07-15 07:11:11.463] [debug] Wrote virtual memory chunk: virt=0x10330, size=0x10
[2025-07-15 07:11:11.463] [debug] Writing virtual memory chunk: virt=0x11340, size=0x1, process=0
[2025-07-15 07:11:11.463] [info] Translating virtual to physical: virt=0x11340, process=0
[2025-07-15 07:11:11.464] [debug] Marked page dirty: virt=0x11340
[2025-07-15 07:11:11.465] [debug] Wrote virtual memory chunk: virt=0x11340, size=0x1
[2025-07-15 07:11:11.465] [debug] Writing virtual memory chunk: virt=0x10340, size=0x10, process=0
[2025-07-15 07:11:11.465] [info] Translating virtual to physical: virt=0x10340, process=0
[2025-07-15 07:11:11.465] [debug] Marked page dirty: virt=0x10340
[2025-07-15 07:11:11.465] [debug] Wrote virtual memory chunk: virt=0x10340, size=0x10
[2025-07-15 07:11:11.465] [debug] Writing virtual memory chunk: virt=0x11350, size=0x1, process=0
[2025-07-15 07:11:11.466] [info] Translating virtual to physical: virt=0x11350, process=0
[2025-07-15 07:11:11.466] [debug] Marked page dirty: virt=0x11350
[2025-07-15 07:11:11.466] [debug] Wrote virtual memory chunk: virt=0x11350, size=0x1
[2025-07-15 07:11:11.466] [debug] Writing virtual memory chunk: virt=0x10350, size=0x10, process=0
[2025-07-15 07:11:11.466] [info] Translating virtual to physical: virt=0x10350, process=0
[2025-07-15 07:11:11.466] [debug] Marked page dirty: virt=0x10350
[2025-07-15 07:11:11.466] [debug] Wrote virtual memory chunk: virt=0x10350, size=0x10
[2025-07-15 07:11:11.466] [debug] Writing virtual memory chunk: virt=0x11360, size=0x1, process=0
[2025-07-15 07:11:11.466] [info] Translating virtual to physical: virt=0x11360, process=0
[2025-07-15 07:11:11.466] [debug] Marked page dirty: virt=0x11360
[2025-07-15 07:11:11.466] [debug] Wrote virtual memory chunk: virt=0x11360, size=0x1
[2025-07-15 07:11:11.466] [debug] Writing virtual memory chunk: virt=0x10360, size=0x10, process=0
[2025-07-15 07:11:11.466] [info] Translating virtual to physical: virt=0x10360, process=0
[2025-07-15 07:11:11.466] [debug] Marked page dirty: virt=0x10360
[2025-07-15 07:11:11.467] [debug] Wrote virtual memory chunk: virt=0x10360, size=0x10
[2025-07-15 07:11:11.467] [debug] Writing virtual memory chunk: virt=0x11370, size=0x1, process=0
[2025-07-15 07:11:11.467] [info] Translating virtual to physical: virt=0x11370, process=0
[2025-07-15 07:11:11.467] [debug] Marked page dirty: virt=0x11370
[2025-07-15 07:11:11.467] [debug] Wrote virtual memory chunk: virt=0x11370, size=0x1
[2025-07-15 07:11:11.467] [debug] Writing virtual memory chunk: virt=0x10370, size=0x10, process=0
[2025-07-15 07:11:11.467] [info] Translating virtual to physical: virt=0x10370, process=0
[2025-07-15 07:11:11.467] [debug] Marked page dirty: virt=0x10370
[2025-07-15 07:11:11.467] [debug] Wrote virtual memory chunk: virt=0x10370, size=0x10
[2025-07-15 07:11:11.467] [debug] Writing virtual memory chunk: virt=0x11380, size=0x1, process=0
[2025-07-15 07:11:11.467] [info] Translating virtual to physical: virt=0x11380, process=0
[2025-07-15 07:11:11.467] [debug] Marked page dirty: virt=0x11380
[2025-07-15 07:11:11.467] [debug] Wrote virtual memory chunk: virt=0x11380, size=0x1
[2025-07-15 07:11:11.467] [debug] Writing virtual memory chunk: virt=0x10380, size=0x10, process=0
[2025-07-15 07:11:11.467] [info] Translating virtual to physical: virt=0x10380, process=0
[2025-07-15 07:11:11.467] [debug] Marked page dirty: virt=0x10380
[2025-07-15 07:11:11.467] [debug] Wrote virtual memory chunk: virt=0x10380, size=0x10
[2025-07-15 07:11:11.468] [debug] Writing virtual memory chunk: virt=0x11390, size=0x1, process=0
[2025-07-15 07:11:11.468] [info] Translating virtual to physical: virt=0x11390, process=0
[2025-07-15 07:11:11.468] [debug] Marked page dirty: virt=0x11390
[2025-07-15 07:11:11.468] [debug] Wrote virtual memory chunk: virt=0x11390, size=0x1
[2025-07-15 07:11:11.468] [debug] Writing virtual memory chunk: virt=0x10390, size=0x10, process=0
[2025-07-15 07:11:11.468] [info] Translating virtual to physical: virt=0x10390, process=0
[2025-07-15 07:11:11.468] [debug] Marked page dirty: virt=0x10390
[2025-07-15 07:11:11.468] [debug] Wrote virtual memory chunk: virt=0x10390, size=0x10
[2025-07-15 07:11:11.468] [debug] Writing virtual memory chunk: virt=0x113a0, size=0x1, process=0
[2025-07-15 07:11:11.468] [info] Translating virtual to physical: virt=0x113a0, process=0
[2025-07-15 07:11:11.468] [debug] Marked page dirty: virt=0x113a0
[2025-07-15 07:11:11.468] [debug] Wrote virtual memory chunk: virt=0x113a0, size=0x1
[2025-07-15 07:11:11.468] [debug] Writing virtual memory chunk: virt=0x103a0, size=0x10, process=0
[2025-07-15 07:11:11.468] [info] Translating virtual to physical: virt=0x103a0, process=0
[2025-07-15 07:11:11.468] [debug] Marked page dirty: virt=0x103a0
[2025-07-15 07:11:11.469] [debug] Wrote virtual memory chunk: virt=0x103a0, size=0x10
[2025-07-15 07:11:11.469] [debug] Writing virtual memory chunk: virt=0x113b0, size=0x1, process=0
[2025-07-15 07:11:11.469] [info] Translating virtual to physical: virt=0x113b0, process=0
[2025-07-15 07:11:11.469] [debug] Marked page dirty: virt=0x113b0
[2025-07-15 07:11:11.469] [debug] Wrote virtual memory chunk: virt=0x113b0, size=0x1
[2025-07-15 07:11:11.469] [debug] Writing virtual memory chunk: virt=0x103b0, size=0x10, process=0
[2025-07-15 07:11:11.469] [info] Translating virtual to physical: virt=0x103b0, process=0
[2025-07-15 07:11:11.469] [debug] Marked page dirty: virt=0x103b0
[2025-07-15 07:11:11.469] [debug] Wrote virtual memory chunk: virt=0x103b0, size=0x10
[2025-07-15 07:11:11.469] [debug] Writing virtual memory chunk: virt=0x113c0, size=0x1, process=0
[2025-07-15 07:11:11.469] [info] Translating virtual to physical: virt=0x113c0, process=0
[2025-07-15 07:11:11.469] [debug] Marked page dirty: virt=0x113c0
[2025-07-15 07:11:11.469] [debug] Wrote virtual memory chunk: virt=0x113c0, size=0x1
[2025-07-15 07:11:11.469] [debug] Writing virtual memory chunk: virt=0x103c0, size=0x10, process=0
[2025-07-15 07:11:11.470] [info] Translating virtual to physical: virt=0x103c0, process=0
[2025-07-15 07:11:11.470] [debug] Marked page dirty: virt=0x103c0
[2025-07-15 07:11:11.470] [debug] Wrote virtual memory chunk: virt=0x103c0, size=0x10
[2025-07-15 07:11:11.470] [debug] Writing virtual memory chunk: virt=0x113d0, size=0x1, process=0
[2025-07-15 07:11:11.470] [info] Translating virtual to physical: virt=0x113d0, process=0
[2025-07-15 07:11:11.470] [debug] Marked page dirty: virt=0x113d0
[2025-07-15 07:11:11.470] [debug] Wrote virtual memory chunk: virt=0x113d0, size=0x1
[2025-07-15 07:11:11.470] [debug] Writing virtual memory chunk: virt=0x103d0, size=0x10, process=0
[2025-07-15 07:11:11.470] [info] Translating virtual to physical: virt=0x103d0, process=0
[2025-07-15 07:11:11.470] [debug] Marked page dirty: virt=0x103d0
[2025-07-15 07:11:11.470] [debug] Wrote virtual memory chunk: virt=0x103d0, size=0x10
[2025-07-15 07:11:11.470] [debug] Writing virtual memory chunk: virt=0x113e0, size=0x1, process=0
[2025-07-15 07:11:11.470] [info] Translating virtual to physical: virt=0x113e0, process=0
[2025-07-15 07:11:11.470] [debug] Marked page dirty: virt=0x113e0
[2025-07-15 07:11:11.471] [debug] Wrote virtual memory chunk: virt=0x113e0, size=0x1
[2025-07-15 07:11:11.471] [debug] Writing virtual memory chunk: virt=0x103e0, size=0x10, process=0
[2025-07-15 07:11:11.471] [info] Translating virtual to physical: virt=0x103e0, process=0
[2025-07-15 07:11:11.471] [debug] Marked page dirty: virt=0x103e0
[2025-07-15 07:11:11.471] [debug] Wrote virtual memory chunk: virt=0x103e0, size=0x10
[2025-07-15 07:11:11.471] [debug] Writing virtual memory chunk: virt=0x113f0, size=0x1, process=0
[2025-07-15 07:11:11.471] [info] Translating virtual to physical: virt=0x113f0, process=0
[2025-07-15 07:11:11.471] [debug] Marked page dirty: virt=0x113f0
[2025-07-15 07:11:11.471] [debug] Wrote virtual memory chunk: virt=0x113f0, size=0x1
[2025-07-15 07:11:11.471] [debug] Writing virtual memory chunk: virt=0x103f0, size=0x10, process=0
[2025-07-15 07:11:11.471] [info] Translating virtual to physical: virt=0x103f0, process=0
[2025-07-15 07:11:11.471] [debug] Marked page dirty: virt=0x103f0
[2025-07-15 07:11:11.471] [debug] Wrote virtual memory chunk: virt=0x103f0, size=0x10
[2025-07-15 07:11:11.471] [debug] Writing virtual memory chunk: virt=0x11400, size=0x1, process=0
[2025-07-15 07:11:11.472] [info] Translating virtual to physical: virt=0x11400, process=0
[2025-07-15 07:11:11.472] [debug] Marked page dirty: virt=0x11400
[2025-07-15 07:11:11.472] [debug] Wrote virtual memory chunk: virt=0x11400, size=0x1
[2025-07-15 07:11:11.472] [debug] Writing virtual memory chunk: virt=0x10400, size=0x10, process=0
[2025-07-15 07:11:11.472] [info] Translating virtual to physical: virt=0x10400, process=0
[2025-07-15 07:11:11.472] [debug] Marked page dirty: virt=0x10400
[2025-07-15 07:11:11.472] [debug] Wrote virtual memory chunk: virt=0x10400, size=0x10
[2025-07-15 07:11:11.472] [debug] Writing virtual memory chunk: virt=0x11410, size=0x1, process=0
[2025-07-15 07:11:11.472] [info] Translating virtual to physical: virt=0x11410, process=0
[2025-07-15 07:11:11.472] [debug] Marked page dirty: virt=0x11410
[2025-07-15 07:11:11.472] [debug] Wrote virtual memory chunk: virt=0x11410, size=0x1
[2025-07-15 07:11:11.472] [debug] Writing virtual memory chunk: virt=0x10410, size=0x10, process=0
[2025-07-15 07:11:11.473] [info] Translating virtual to physical: virt=0x10410, process=0
[2025-07-15 07:11:11.473] [debug] Marked page dirty: virt=0x10410
[2025-07-15 07:11:11.473] [debug] Wrote virtual memory chunk: virt=0x10410, size=0x10
[2025-07-15 07:11:11.473] [debug] Writing virtual memory chunk: virt=0x11420, size=0x1, process=0
[2025-07-15 07:11:11.473] [info] Translating virtual to physical: virt=0x11420, process=0
[2025-07-15 07:11:11.473] [debug] Marked page dirty: virt=0x11420
[2025-07-15 07:11:11.473] [debug] Wrote virtual memory chunk: virt=0x11420, size=0x1
[2025-07-15 07:11:11.473] [debug] Writing virtual memory chunk: virt=0x10420, size=0x10, process=0
[2025-07-15 07:11:11.473] [info] Translating virtual to physical: virt=0x10420, process=0
[2025-07-15 07:11:11.473] [debug] Marked page dirty: virt=0x10420
[2025-07-15 07:11:11.473] [debug] Wrote virtual memory chunk: virt=0x10420, size=0x10
[2025-07-15 07:11:11.473] [debug] Writing virtual memory chunk: virt=0x11430, size=0x1, process=0
[2025-07-15 07:11:11.473] [info] Translating virtual to physical: virt=0x11430, process=0
[2025-07-15 07:11:11.474] [debug] Marked page dirty: virt=0x11430
[2025-07-15 07:11:11.474] [debug] Wrote virtual memory chunk: virt=0x11430, size=0x1
[2025-07-15 07:11:11.474] [debug] Writing virtual memory chunk: virt=0x10430, size=0x10, process=0
[2025-07-15 07:11:11.474] [info] Translating virtual to physical: virt=0x10430, process=0
[2025-07-15 07:11:11.474] [debug] Marked page dirty: virt=0x10430
[2025-07-15 07:11:11.474] [debug] Wrote virtual memory chunk: virt=0x10430, size=0x10
[2025-07-15 07:11:11.474] [debug] Writing virtual memory chunk: virt=0x11440, size=0x1, process=0
[2025-07-15 07:11:11.474] [info] Translating virtual to physical: virt=0x11440, process=0
[2025-07-15 07:11:11.474] [debug] Marked page dirty: virt=0x11440
[2025-07-15 07:11:11.474] [debug] Wrote virtual memory chunk: virt=0x11440, size=0x1
[2025-07-15 07:11:11.474] [debug] Writing virtual memory chunk: virt=0x10440, size=0x10, process=0
[2025-07-15 07:11:11.474] [info] Translating virtual to physical: virt=0x10440, process=0
[2025-07-15 07:11:11.474] [debug] Marked page dirty: virt=0x10440
[2025-07-15 07:11:11.474] [debug] Wrote virtual memory chunk: virt=0x10440, size=0x10
[2025-07-15 07:11:11.474] [debug] Writing virtual memory chunk: virt=0x11450, size=0x1, process=0
[2025-07-15 07:11:11.474] [info] Translating virtual to physical: virt=0x11450, process=0
[2025-07-15 07:11:11.475] [debug] Marked page dirty: virt=0x11450
[2025-07-15 07:11:11.475] [debug] Wrote virtual memory chunk: virt=0x11450, size=0x1
[2025-07-15 07:11:11.475] [debug] Writing virtual memory chunk: virt=0x10450, size=0x10, process=0
[2025-07-15 07:11:11.475] [info] Translating virtual to physical: virt=0x10450, process=0
[2025-07-15 07:11:11.475] [debug] Marked page dirty: virt=0x10450
[2025-07-15 07:11:11.475] [debug] Wrote virtual memory chunk: virt=0x10450, size=0x10
[2025-07-15 07:11:11.475] [debug] Writing virtual memory chunk: virt=0x11460, size=0x1, process=0
[2025-07-15 07:11:11.475] [info] Translating virtual to physical: virt=0x11460, process=0
[2025-07-15 07:11:11.475] [debug] Marked page dirty: virt=0x11460
[2025-07-15 07:11:11.475] [debug] Wrote virtual memory chunk: virt=0x11460, size=0x1
[2025-07-15 07:11:11.475] [debug] Writing virtual memory chunk: virt=0x10460, size=0x10, process=0
[2025-07-15 07:11:11.475] [info] Translating virtual to physical: virt=0x10460, process=0
[2025-07-15 07:11:11.475] [debug] Marked page dirty: virt=0x10460
[2025-07-15 07:11:11.475] [debug] Wrote virtual memory chunk: virt=0x10460, size=0x10
[2025-07-15 07:11:11.475] [debug] Writing virtual memory chunk: virt=0x11470, size=0x1, process=0
[2025-07-15 07:11:11.476] [info] Translating virtual to physical: virt=0x11470, process=0
[2025-07-15 07:11:11.476] [debug] Marked page dirty: virt=0x11470
[2025-07-15 07:11:11.476] [debug] Wrote virtual memory chunk: virt=0x11470, size=0x1
[2025-07-15 07:11:11.476] [debug] Writing virtual memory chunk: virt=0x10470, size=0x10, process=0
[2025-07-15 07:11:11.476] [info] Translating virtual to physical: virt=0x10470, process=0
[2025-07-15 07:11:11.476] [debug] Marked page dirty: virt=0x10470
[2025-07-15 07:11:11.476] [debug] Wrote virtual memory chunk: virt=0x10470, size=0x10
[2025-07-15 07:11:11.476] [debug] Writing virtual memory chunk: virt=0x11480, size=0x1, process=0
[2025-07-15 07:11:11.476] [info] Translating virtual to physical: virt=0x11480, process=0
[2025-07-15 07:11:11.476] [debug] Marked page dirty: virt=0x11480
[2025-07-15 07:11:11.476] [debug] Wrote virtual memory chunk: virt=0x11480, size=0x1
[2025-07-15 07:11:11.476] [debug] Writing virtual memory chunk: virt=0x10480, size=0x10, process=0
[2025-07-15 07:11:11.476] [info] Translating virtual to physical: virt=0x10480, process=0
[2025-07-15 07:11:11.476] [debug] Marked page dirty: virt=0x10480
[2025-07-15 07:11:11.477] [debug] Wrote virtual memory chunk: virt=0x10480, size=0x10
[2025-07-15 07:11:11.477] [debug] Writing virtual memory chunk: virt=0x11490, size=0x1, process=0
[2025-07-15 07:11:11.477] [info] Translating virtual to physical: virt=0x11490, process=0
[2025-07-15 07:11:11.477] [debug] Marked page dirty: virt=0x11490
[2025-07-15 07:11:11.477] [debug] Wrote virtual memory chunk: virt=0x11490, size=0x1
[2025-07-15 07:11:11.477] [debug] Writing virtual memory chunk: virt=0x10490, size=0x10, process=0
[2025-07-15 07:11:11.477] [info] Translating virtual to physical: virt=0x10490, process=0
[2025-07-15 07:11:11.477] [debug] Marked page dirty: virt=0x10490
[2025-07-15 07:11:11.477] [debug] Wrote virtual memory chunk: virt=0x10490, size=0x10
[2025-07-15 07:11:11.477] [debug] Writing virtual memory chunk: virt=0x114a0, size=0x1, process=0
[2025-07-15 07:11:11.477] [info] Translating virtual to physical: virt=0x114a0, process=0
[2025-07-15 07:11:11.477] [debug] Marked page dirty: virt=0x114a0
[2025-07-15 07:11:11.477] [debug] Wrote virtual memory chunk: virt=0x114a0, size=0x1
[2025-07-15 07:11:11.478] [debug] Writing virtual memory chunk: virt=0x104a0, size=0x10, process=0
[2025-07-15 07:11:11.478] [info] Translating virtual to physical: virt=0x104a0, process=0
[2025-07-15 07:11:11.478] [debug] Marked page dirty: virt=0x104a0
[2025-07-15 07:11:11.478] [debug] Wrote virtual memory chunk: virt=0x104a0, size=0x10
[2025-07-15 07:11:11.478] [debug] Writing virtual memory chunk: virt=0x114b0, size=0x1, process=0
[2025-07-15 07:11:11.478] [info] Translating virtual to physical: virt=0x114b0, process=0
[2025-07-15 07:11:11.478] [debug] Marked page dirty: virt=0x114b0
[2025-07-15 07:11:11.478] [debug] Wrote virtual memory chunk: virt=0x114b0, size=0x1
[2025-07-15 07:11:11.478] [debug] Writing virtual memory chunk: virt=0x104b0, size=0x10, process=0
[2025-07-15 07:11:11.478] [info] Translating virtual to physical: virt=0x104b0, process=0
[2025-07-15 07:11:11.478] [debug] Marked page dirty: virt=0x104b0
[2025-07-15 07:11:11.478] [debug] Wrote virtual memory chunk: virt=0x104b0, size=0x10
[2025-07-15 07:11:11.478] [debug] Writing virtual memory chunk: virt=0x114c0, size=0x1, process=0
[2025-07-15 07:11:11.478] [info] Translating virtual to physical: virt=0x114c0, process=0
[2025-07-15 07:11:11.479] [debug] Marked page dirty: virt=0x114c0
[2025-07-15 07:11:11.479] [debug] Wrote virtual memory chunk: virt=0x114c0, size=0x1
[2025-07-15 07:11:11.479] [debug] Writing virtual memory chunk: virt=0x104c0, size=0x10, process=0
[2025-07-15 07:11:11.479] [info] Translating virtual to physical: virt=0x104c0, process=0
[2025-07-15 07:11:11.479] [debug] Marked page dirty: virt=0x104c0
[2025-07-15 07:11:11.479] [debug] Wrote virtual memory chunk: virt=0x104c0, size=0x10
[2025-07-15 07:11:11.479] [debug] Writing virtual memory chunk: virt=0x114d0, size=0x1, process=0
[2025-07-15 07:11:11.481] [info] Translating virtual to physical: virt=0x114d0, process=0
[2025-07-15 07:11:11.481] [debug] Marked page dirty: virt=0x114d0
[2025-07-15 07:11:11.481] [debug] Wrote virtual memory chunk: virt=0x114d0, size=0x1
[2025-07-15 07:11:11.482] [debug] Writing virtual memory chunk: virt=0x104d0, size=0x10, process=0
[2025-07-15 07:11:11.482] [info] Translating virtual to physical: virt=0x104d0, process=0
[2025-07-15 07:11:11.482] [debug] Marked page dirty: virt=0x104d0
[2025-07-15 07:11:11.482] [debug] Wrote virtual memory chunk: virt=0x104d0, size=0x10
[2025-07-15 07:11:11.482] [debug] Writing virtual memory chunk: virt=0x114e0, size=0x1, process=0
[2025-07-15 07:11:11.482] [info] Translating virtual to physical: virt=0x114e0, process=0
[2025-07-15 07:11:11.482] [debug] Marked page dirty: virt=0x114e0
[2025-07-15 07:11:11.482] [debug] Wrote virtual memory chunk: virt=0x114e0, size=0x1
[2025-07-15 07:11:11.482] [debug] Writing virtual memory chunk: virt=0x104e0, size=0x10, process=0
[2025-07-15 07:11:11.482] [info] Translating virtual to physical: virt=0x104e0, process=0
[2025-07-15 07:11:11.482] [debug] Marked page dirty: virt=0x104e0
[2025-07-15 07:11:11.482] [debug] Wrote virtual memory chunk: virt=0x104e0, size=0x10
[2025-07-15 07:11:11.482] [debug] Writing virtual memory chunk: virt=0x114f0, size=0x1, process=0
[2025-07-15 07:11:11.482] [info] Translating virtual to physical: virt=0x114f0, process=0
[2025-07-15 07:11:11.482] [debug] Marked page dirty: virt=0x114f0
[2025-07-15 07:11:11.483] [debug] Wrote virtual memory chunk: virt=0x114f0, size=0x1
[2025-07-15 07:11:11.483] [debug] Writing virtual memory chunk: virt=0x104f0, size=0x10, process=0
[2025-07-15 07:11:11.483] [info] Translating virtual to physical: virt=0x104f0, process=0
[2025-07-15 07:11:11.483] [debug] Marked page dirty: virt=0x104f0
[2025-07-15 07:11:11.483] [debug] Wrote virtual memory chunk: virt=0x104f0, size=0x10
[2025-07-15 07:11:11.483] [debug] Writing virtual memory chunk: virt=0x11500, size=0x1, process=0
[2025-07-15 07:11:11.483] [info] Translating virtual to physical: virt=0x11500, process=0
[2025-07-15 07:11:11.483] [debug] Marked page dirty: virt=0x11500
[2025-07-15 07:11:11.483] [debug] Wrote virtual memory chunk: virt=0x11500, size=0x1
[2025-07-15 07:11:11.483] [debug] Writing virtual memory chunk: virt=0x10500, size=0x10, process=0
[2025-07-15 07:11:11.483] [info] Translating virtual to physical: virt=0x10500, process=0
[2025-07-15 07:11:11.483] [debug] Marked page dirty: virt=0x10500
[2025-07-15 07:11:11.483] [debug] Wrote virtual memory chunk: virt=0x10500, size=0x10
[2025-07-15 07:11:11.483] [debug] Writing virtual memory chunk: virt=0x11510, size=0x1, process=0
[2025-07-15 07:11:11.483] [info] Translating virtual to physical: virt=0x11510, process=0
[2025-07-15 07:11:11.483] [debug] Marked page dirty: virt=0x11510
[2025-07-15 07:11:11.483] [debug] Wrote virtual memory chunk: virt=0x11510, size=0x1
[2025-07-15 07:11:11.484] [debug] Writing virtual memory chunk: virt=0x10510, size=0x10, process=0
[2025-07-15 07:11:11.484] [info] Translating virtual to physical: virt=0x10510, process=0
[2025-07-15 07:11:11.484] [debug] Marked page dirty: virt=0x10510
[2025-07-15 07:11:11.484] [debug] Wrote virtual memory chunk: virt=0x10510, size=0x10
[2025-07-15 07:11:11.484] [debug] Writing virtual memory chunk: virt=0x11520, size=0x1, process=0
[2025-07-15 07:11:11.484] [info] Translating virtual to physical: virt=0x11520, process=0
[2025-07-15 07:11:11.484] [debug] Marked page dirty: virt=0x11520
[2025-07-15 07:11:11.484] [debug] Wrote virtual memory chunk: virt=0x11520, size=0x1
[2025-07-15 07:11:11.484] [debug] Writing virtual memory chunk: virt=0x10520, size=0x10, process=0
[2025-07-15 07:11:11.484] [info] Translating virtual to physical: virt=0x10520, process=0
[2025-07-15 07:11:11.484] [debug] Marked page dirty: virt=0x10520
[2025-07-15 07:11:11.484] [debug] Wrote virtual memory chunk: virt=0x10520, size=0x10
[2025-07-15 07:11:11.484] [debug] Writing virtual memory chunk: virt=0x11530, size=0x1, process=0
[2025-07-15 07:11:11.485] [info] Translating virtual to physical: virt=0x11530, process=0
[2025-07-15 07:11:11.485] [debug] Marked page dirty: virt=0x11530
[2025-07-15 07:11:11.485] [debug] Wrote virtual memory chunk: virt=0x11530, size=0x1
[2025-07-15 07:11:11.485] [debug] Writing virtual memory chunk: virt=0x10530, size=0x10, process=0
[2025-07-15 07:11:11.485] [info] Translating virtual to physical: virt=0x10530, process=0
[2025-07-15 07:11:11.485] [debug] Marked page dirty: virt=0x10530
[2025-07-15 07:11:11.485] [debug] Wrote virtual memory chunk: virt=0x10530, size=0x10
[2025-07-15 07:11:11.485] [debug] Writing virtual memory chunk: virt=0x11540, size=0x1, process=0
[2025-07-15 07:11:11.485] [info] Translating virtual to physical: virt=0x11540, process=0
[2025-07-15 07:11:11.485] [debug] Marked page dirty: virt=0x11540
[2025-07-15 07:11:11.485] [debug] Wrote virtual memory chunk: virt=0x11540, size=0x1
[2025-07-15 07:11:11.485] [debug] Writing virtual memory chunk: virt=0x10540, size=0x10, process=0
[2025-07-15 07:11:11.485] [info] Translating virtual to physical: virt=0x10540, process=0
[2025-07-15 07:11:11.485] [debug] Marked page dirty: virt=0x10540
[2025-07-15 07:11:11.486] [debug] Wrote virtual memory chunk: virt=0x10540, size=0x10
[2025-07-15 07:11:11.486] [debug] Writing virtual memory chunk: virt=0x11550, size=0x1, process=0
[2025-07-15 07:11:11.486] [info] Translating virtual to physical: virt=0x11550, process=0
[2025-07-15 07:11:11.486] [debug] Marked page dirty: virt=0x11550
[2025-07-15 07:11:11.486] [debug] Wrote virtual memory chunk: virt=0x11550, size=0x1
[2025-07-15 07:11:11.486] [debug] Writing virtual memory chunk: virt=0x10550, size=0x10, process=0
[2025-07-15 07:11:11.486] [info] Translating virtual to physical: virt=0x10550, process=0
[2025-07-15 07:11:11.486] [debug] Marked page dirty: virt=0x10550
[2025-07-15 07:11:11.486] [debug] Wrote virtual memory chunk: virt=0x10550, size=0x10
[2025-07-15 07:11:11.486] [debug] Writing virtual memory chunk: virt=0x11560, size=0x1, process=0
[2025-07-15 07:11:11.486] [info] Translating virtual to physical: virt=0x11560, process=0
[2025-07-15 07:11:11.486] [debug] Marked page dirty: virt=0x11560
[2025-07-15 07:11:11.486] [debug] Wrote virtual memory chunk: virt=0x11560, size=0x1
[2025-07-15 07:11:11.487] [debug] Writing virtual memory chunk: virt=0x10560, size=0x10, process=0
[2025-07-15 07:11:11.487] [info] Translating virtual to physical: virt=0x10560, process=0
[2025-07-15 07:11:11.487] [debug] Marked page dirty: virt=0x10560
[2025-07-15 07:11:11.487] [debug] Wrote virtual memory chunk: virt=0x10560, size=0x10
[2025-07-15 07:11:11.487] [debug] Writing virtual memory chunk: virt=0x11570, size=0x1, process=0
[2025-07-15 07:11:11.487] [info] Translating virtual to physical: virt=0x11570, process=0
[2025-07-15 07:11:11.487] [debug] Marked page dirty: virt=0x11570
[2025-07-15 07:11:11.487] [debug] Wrote virtual memory chunk: virt=0x11570, size=0x1
[2025-07-15 07:11:11.487] [debug] Writing virtual memory chunk: virt=0x10570, size=0x10, process=0
[2025-07-15 07:11:11.487] [info] Translating virtual to physical: virt=0x10570, process=0
[2025-07-15 07:11:11.487] [debug] Marked page dirty: virt=0x10570
[2025-07-15 07:11:11.487] [debug] Wrote virtual memory chunk: virt=0x10570, size=0x10
[2025-07-15 07:11:11.487] [debug] Writing virtual memory chunk: virt=0x11580, size=0x1, process=0
[2025-07-15 07:11:11.488] [info] Translating virtual to physical: virt=0x11580, process=0
[2025-07-15 07:11:11.488] [debug] Marked page dirty: virt=0x11580
[2025-07-15 07:11:11.488] [debug] Wrote virtual memory chunk: virt=0x11580, size=0x1
[2025-07-15 07:11:11.488] [debug] Writing virtual memory chunk: virt=0x10580, size=0x10, process=0
[2025-07-15 07:11:11.488] [info] Translating virtual to physical: virt=0x10580, process=0
[2025-07-15 07:11:11.488] [debug] Marked page dirty: virt=0x10580
[2025-07-15 07:11:11.488] [debug] Wrote virtual memory chunk: virt=0x10580, size=0x10
[2025-07-15 07:11:11.488] [debug] Writing virtual memory chunk: virt=0x11590, size=0x1, process=0
[2025-07-15 07:11:11.488] [info] Translating virtual to physical: virt=0x11590, process=0
[2025-07-15 07:11:11.488] [debug] Marked page dirty: virt=0x11590
[2025-07-15 07:11:11.489] [debug] Wrote virtual memory chunk: virt=0x11590, size=0x1
[2025-07-15 07:11:11.489] [debug] Writing virtual memory chunk: virt=0x10590, size=0x10, process=0
[2025-07-15 07:11:11.489] [info] Translating virtual to physical: virt=0x10590, process=0
[2025-07-15 07:11:11.489] [debug] Marked page dirty: virt=0x10590
[2025-07-15 07:11:11.489] [debug] Wrote virtual memory chunk: virt=0x10590, size=0x10
[2025-07-15 07:11:11.489] [debug] Writing virtual memory chunk: virt=0x115a0, size=0x1, process=0
[2025-07-15 07:11:11.489] [info] Translating virtual to physical: virt=0x115a0, process=0
[2025-07-15 07:11:11.489] [debug] Marked page dirty: virt=0x115a0
[2025-07-15 07:11:11.489] [debug] Wrote virtual memory chunk: virt=0x115a0, size=0x1
[2025-07-15 07:11:11.489] [debug] Writing virtual memory chunk: virt=0x105a0, size=0x10, process=0
[2025-07-15 07:11:11.489] [info] Translating virtual to physical: virt=0x105a0, process=0
[2025-07-15 07:11:11.490] [debug] Marked page dirty: virt=0x105a0
[2025-07-15 07:11:11.490] [debug] Wrote virtual memory chunk: virt=0x105a0, size=0x10
[2025-07-15 07:11:11.490] [debug] Writing virtual memory chunk: virt=0x115b0, size=0x1, process=0
[2025-07-15 07:11:11.490] [info] Translating virtual to physical: virt=0x115b0, process=0
[2025-07-15 07:11:11.490] [debug] Marked page dirty: virt=0x115b0
[2025-07-15 07:11:11.490] [debug] Wrote virtual memory chunk: virt=0x115b0, size=0x1
[2025-07-15 07:11:11.490] [debug] Writing virtual memory chunk: virt=0x105b0, size=0x10, process=0
[2025-07-15 07:11:11.490] [info] Translating virtual to physical: virt=0x105b0, process=0
[2025-07-15 07:11:11.490] [debug] Marked page dirty: virt=0x105b0
[2025-07-15 07:11:11.490] [debug] Wrote virtual memory chunk: virt=0x105b0, size=0x10
[2025-07-15 07:11:11.490] [debug] Writing virtual memory chunk: virt=0x115c0, size=0x1, process=0
[2025-07-15 07:11:11.490] [info] Translating virtual to physical: virt=0x115c0, process=0
[2025-07-15 07:11:11.490] [debug] Marked page dirty: virt=0x115c0
[2025-07-15 07:11:11.490] [debug] Wrote virtual memory chunk: virt=0x115c0, size=0x1
[2025-07-15 07:11:11.490] [debug] Writing virtual memory chunk: virt=0x105c0, size=0x10, process=0
[2025-07-15 07:11:11.490] [info] Translating virtual to physical: virt=0x105c0, process=0
[2025-07-15 07:11:11.490] [debug] Marked page dirty: virt=0x105c0
[2025-07-15 07:11:11.490] [debug] Wrote virtual memory chunk: virt=0x105c0, size=0x10
[2025-07-15 07:11:11.490] [debug] Writing virtual memory chunk: virt=0x115d0, size=0x1, process=0
[2025-07-15 07:11:11.491] [info] Translating virtual to physical: virt=0x115d0, process=0
[2025-07-15 07:11:11.491] [debug] Marked page dirty: virt=0x115d0
[2025-07-15 07:11:11.491] [debug] Wrote virtual memory chunk: virt=0x115d0, size=0x1
[2025-07-15 07:11:11.491] [debug] Writing virtual memory chunk: virt=0x105d0, size=0x10, process=0
[2025-07-15 07:11:11.491] [info] Translating virtual to physical: virt=0x105d0, process=0
[2025-07-15 07:11:11.491] [debug] Marked page dirty: virt=0x105d0
[2025-07-15 07:11:11.491] [debug] Wrote virtual memory chunk: virt=0x105d0, size=0x10
[2025-07-15 07:11:11.491] [debug] Writing virtual memory chunk: virt=0x115e0, size=0x1, process=0
[2025-07-15 07:11:11.491] [info] Translating virtual to physical: virt=0x115e0, process=0
[2025-07-15 07:11:11.491] [debug] Marked page dirty: virt=0x115e0
[2025-07-15 07:11:11.491] [debug] Wrote virtual memory chunk: virt=0x115e0, size=0x1
[2025-07-15 07:11:11.491] [debug] Writing virtual memory chunk: virt=0x105e0, size=0x10, process=0
[2025-07-15 07:11:11.491] [info] Translating virtual to physical: virt=0x105e0, process=0
[2025-07-15 07:11:11.491] [debug] Marked page dirty: virt=0x105e0
[2025-07-15 07:11:11.492] [debug] Wrote virtual memory chunk: virt=0x105e0, size=0x10
[2025-07-15 07:11:11.492] [debug] Writing virtual memory chunk: virt=0x115f0, size=0x1, process=0
[2025-07-15 07:11:11.492] [info] Translating virtual to physical: virt=0x115f0, process=0
[2025-07-15 07:11:11.492] [debug] Marked page dirty: virt=0x115f0
[2025-07-15 07:11:11.492] [debug] Wrote virtual memory chunk: virt=0x115f0, size=0x1
[2025-07-15 07:11:11.492] [debug] Writing virtual memory chunk: virt=0x105f0, size=0x10, process=0
[2025-07-15 07:11:11.492] [info] Translating virtual to physical: virt=0x105f0, process=0
[2025-07-15 07:11:11.492] [debug] Marked page dirty: virt=0x105f0
[2025-07-15 07:11:11.492] [debug] Wrote virtual memory chunk: virt=0x105f0, size=0x10
[2025-07-15 07:11:11.492] [debug] Writing virtual memory chunk: virt=0x11600, size=0x1, process=0
[2025-07-15 07:11:11.492] [info] Translating virtual to physical: virt=0x11600, process=0
[2025-07-15 07:11:11.492] [debug] Marked page dirty: virt=0x11600
[2025-07-15 07:11:11.492] [debug] Wrote virtual memory chunk: virt=0x11600, size=0x1
[2025-07-15 07:11:11.492] [debug] Writing virtual memory chunk: virt=0x10600, size=0x10, process=0
[2025-07-15 07:11:11.493] [info] Translating virtual to physical: virt=0x10600, process=0
[2025-07-15 07:11:11.493] [debug] Marked page dirty: virt=0x10600
[2025-07-15 07:11:11.493] [debug] Wrote virtual memory chunk: virt=0x10600, size=0x10
[2025-07-15 07:11:11.493] [debug] Writing virtual memory chunk: virt=0x11610, size=0x1, process=0
[2025-07-15 07:11:11.493] [info] Translating virtual to physical: virt=0x11610, process=0
[2025-07-15 07:11:11.493] [debug] Marked page dirty: virt=0x11610
[2025-07-15 07:11:11.493] [debug] Wrote virtual memory chunk: virt=0x11610, size=0x1
[2025-07-15 07:11:11.493] [debug] Writing virtual memory chunk: virt=0x10610, size=0x10, process=0
[2025-07-15 07:11:11.493] [info] Translating virtual to physical: virt=0x10610, process=0
[2025-07-15 07:11:11.493] [debug] Marked page dirty: virt=0x10610
[2025-07-15 07:11:11.493] [debug] Wrote virtual memory chunk: virt=0x10610, size=0x10
[2025-07-15 07:11:11.493] [debug] Writing virtual memory chunk: virt=0x11620, size=0x1, process=0
[2025-07-15 07:11:11.493] [info] Translating virtual to physical: virt=0x11620, process=0
[2025-07-15 07:11:11.493] [debug] Marked page dirty: virt=0x11620
[2025-07-15 07:11:11.493] [debug] Wrote virtual memory chunk: virt=0x11620, size=0x1
[2025-07-15 07:11:11.493] [debug] Writing virtual memory chunk: virt=0x10620, size=0x10, process=0
[2025-07-15 07:11:11.494] [info] Translating virtual to physical: virt=0x10620, process=0
[2025-07-15 07:11:11.494] [debug] Marked page dirty: virt=0x10620
[2025-07-15 07:11:11.494] [debug] Wrote virtual memory chunk: virt=0x10620, size=0x10
[2025-07-15 07:11:11.494] [debug] Writing virtual memory chunk: virt=0x11630, size=0x1, process=0
[2025-07-15 07:11:11.494] [info] Translating virtual to physical: virt=0x11630, process=0
[2025-07-15 07:11:11.494] [debug] Marked page dirty: virt=0x11630
[2025-07-15 07:11:11.494] [debug] Wrote virtual memory chunk: virt=0x11630, size=0x1
[2025-07-15 07:11:11.494] [debug] Writing virtual memory chunk: virt=0x10630, size=0x10, process=0
[2025-07-15 07:11:11.494] [info] Translating virtual to physical: virt=0x10630, process=0
[2025-07-15 07:11:11.494] [debug] Marked page dirty: virt=0x10630
[2025-07-15 07:11:11.494] [debug] Wrote virtual memory chunk: virt=0x10630, size=0x10
[2025-07-15 07:11:11.494] [debug] Writing virtual memory chunk: virt=0x11640, size=0x1, process=0
[2025-07-15 07:11:11.494] [info] Translating virtual to physical: virt=0x11640, process=0
[2025-07-15 07:11:11.494] [debug] Marked page dirty: virt=0x11640
[2025-07-15 07:11:11.494] [debug] Wrote virtual memory chunk: virt=0x11640, size=0x1
[2025-07-15 07:11:11.495] [debug] Writing virtual memory chunk: virt=0x10640, size=0x10, process=0
[2025-07-15 07:11:11.496] [info] Translating virtual to physical: virt=0x10640, process=0
[2025-07-15 07:11:11.496] [debug] Marked page dirty: virt=0x10640
[2025-07-15 07:11:11.496] [debug] Wrote virtual memory chunk: virt=0x10640, size=0x10
[2025-07-15 07:11:11.497] [info] Processed descriptor 100
[2025-07-15 07:11:11.497] [debug] Writing virtual memory chunk: virt=0x11650, size=0x1, process=0
[2025-07-15 07:11:11.497] [info] Translating virtual to physical: virt=0x11650, process=0
[2025-07-15 07:11:11.497] [debug] Marked page dirty: virt=0x11650
[2025-07-15 07:11:11.497] [debug] Wrote virtual memory chunk: virt=0x11650, size=0x1
[2025-07-15 07:11:11.497] [debug] Writing virtual memory chunk: virt=0x10650, size=0x10, process=0
[2025-07-15 07:11:11.497] [info] Translating virtual to physical: virt=0x10650, process=0
[2025-07-15 07:11:11.497] [debug] Marked page dirty: virt=0x10650
[2025-07-15 07:11:11.497] [debug] Wrote virtual memory chunk: virt=0x10650, size=0x10
[2025-07-15 07:11:11.497] [debug] Writing virtual memory chunk: virt=0x11660, size=0x1, process=0
[2025-07-15 07:11:11.497] [info] Translating virtual to physical: virt=0x11660, process=0
[2025-07-15 07:11:11.497] [debug] Marked page dirty: virt=0x11660
[2025-07-15 07:11:11.497] [debug] Wrote virtual memory chunk: virt=0x11660, size=0x1
[2025-07-15 07:11:11.497] [debug] Writing virtual memory chunk: virt=0x10660, size=0x10, process=0
[2025-07-15 07:11:11.497] [info] Translating virtual to physical: virt=0x10660, process=0
[2025-07-15 07:11:11.497] [debug] Marked page dirty: virt=0x10660
[2025-07-15 07:11:11.498] [debug] Wrote virtual memory chunk: virt=0x10660, size=0x10
[2025-07-15 07:11:11.498] [debug] Writing virtual memory chunk: virt=0x11670, size=0x1, process=0
[2025-07-15 07:11:11.498] [info] Translating virtual to physical: virt=0x11670, process=0
[2025-07-15 07:11:11.498] [debug] Marked page dirty: virt=0x11670
[2025-07-15 07:11:11.498] [debug] Wrote virtual memory chunk: virt=0x11670, size=0x1
[2025-07-15 07:11:11.498] [debug] Writing virtual memory chunk: virt=0x10670, size=0x10, process=0
[2025-07-15 07:11:11.498] [info] Translating virtual to physical: virt=0x10670, process=0
[2025-07-15 07:11:11.498] [debug] Marked page dirty: virt=0x10670
[2025-07-15 07:11:11.498] [debug] Wrote virtual memory chunk: virt=0x10670, size=0x10
[2025-07-15 07:11:11.498] [debug] Writing virtual memory chunk: virt=0x11680, size=0x1, process=0
[2025-07-15 07:11:11.498] [info] Translating virtual to physical: virt=0x11680, process=0
[2025-07-15 07:11:11.498] [debug] Marked page dirty: virt=0x11680
[2025-07-15 07:11:11.498] [debug] Wrote virtual memory chunk: virt=0x11680, size=0x1
[2025-07-15 07:11:11.498] [debug] Writing virtual memory chunk: virt=0x10680, size=0x10, process=0
[2025-07-15 07:11:11.498] [info] Translating virtual to physical: virt=0x10680, process=0
[2025-07-15 07:11:11.498] [debug] Marked page dirty: virt=0x10680
[2025-07-15 07:11:11.499] [debug] Wrote virtual memory chunk: virt=0x10680, size=0x10
[2025-07-15 07:11:11.499] [debug] Writing virtual memory chunk: virt=0x11690, size=0x1, process=0
[2025-07-15 07:11:11.499] [info] Translating virtual to physical: virt=0x11690, process=0
[2025-07-15 07:11:11.499] [debug] Marked page dirty: virt=0x11690
[2025-07-15 07:11:11.499] [debug] Wrote virtual memory chunk: virt=0x11690, size=0x1
[2025-07-15 07:11:11.499] [debug] Writing virtual memory chunk: virt=0x10690, size=0x10, process=0
[2025-07-15 07:11:11.499] [info] Translating virtual to physical: virt=0x10690, process=0
[2025-07-15 07:11:11.499] [debug] Marked page dirty: virt=0x10690
[2025-07-15 07:11:11.499] [debug] Wrote virtual memory chunk: virt=0x10690, size=0x10
[2025-07-15 07:11:11.499] [debug] Writing virtual memory chunk: virt=0x116a0, size=0x1, process=0
[2025-07-15 07:11:11.499] [info] Translating virtual to physical: virt=0x116a0, process=0
[2025-07-15 07:11:11.499] [debug] Marked page dirty: virt=0x116a0
[2025-07-15 07:11:11.499] [debug] Wrote virtual memory chunk: virt=0x116a0, size=0x1
[2025-07-15 07:11:11.499] [debug] Writing virtual memory chunk: virt=0x106a0, size=0x10, process=0
[2025-07-15 07:11:11.499] [info] Translating virtual to physical: virt=0x106a0, process=0
[2025-07-15 07:11:11.499] [debug] Marked page dirty: virt=0x106a0
[2025-07-15 07:11:11.500] [debug] Wrote virtual memory chunk: virt=0x106a0, size=0x10
[2025-07-15 07:11:11.500] [debug] Writing virtual memory chunk: virt=0x116b0, size=0x1, process=0
[2025-07-15 07:11:11.500] [info] Translating virtual to physical: virt=0x116b0, process=0
[2025-07-15 07:11:11.500] [debug] Marked page dirty: virt=0x116b0
[2025-07-15 07:11:11.500] [debug] Wrote virtual memory chunk: virt=0x116b0, size=0x1
[2025-07-15 07:11:11.500] [debug] Writing virtual memory chunk: virt=0x106b0, size=0x10, process=0
[2025-07-15 07:11:11.500] [info] Translating virtual to physical: virt=0x106b0, process=0
[2025-07-15 07:11:11.500] [debug] Marked page dirty: virt=0x106b0
[2025-07-15 07:11:11.500] [debug] Wrote virtual memory chunk: virt=0x106b0, size=0x10
[2025-07-15 07:11:11.500] [debug] Writing virtual memory chunk: virt=0x116c0, size=0x1, process=0
[2025-07-15 07:11:11.500] [info] Translating virtual to physical: virt=0x116c0, process=0
[2025-07-15 07:11:11.500] [debug] Marked page dirty: virt=0x116c0
[2025-07-15 07:11:11.500] [debug] Wrote virtual memory chunk: virt=0x116c0, size=0x1
[2025-07-15 07:11:11.500] [debug] Writing virtual memory chunk: virt=0x106c0, size=0x10, process=0
[2025-07-15 07:11:11.500] [info] Translating virtual to physical: virt=0x106c0, process=0
[2025-07-15 07:11:11.500] [debug] Marked page dirty: virt=0x106c0
[2025-07-15 07:11:11.501] [debug] Wrote virtual memory chunk: virt=0x106c0, size=0x10
[2025-07-15 07:11:11.501] [debug] Writing virtual memory chunk: virt=0x116d0, size=0x1, process=0
[2025-07-15 07:11:11.501] [info] Translating virtual to physical: virt=0x116d0, process=0
[2025-07-15 07:11:11.501] [debug] Marked page dirty: virt=0x116d0
[2025-07-15 07:11:11.501] [debug] Wrote virtual memory chunk: virt=0x116d0, size=0x1
[2025-07-15 07:11:11.501] [debug] Writing virtual memory chunk: virt=0x106d0, size=0x10, process=0
[2025-07-15 07:11:11.501] [info] Translating virtual to physical: virt=0x106d0, process=0
[2025-07-15 07:11:11.501] [debug] Marked page dirty: virt=0x106d0
[2025-07-15 07:11:11.501] [debug] Wrote virtual memory chunk: virt=0x106d0, size=0x10
[2025-07-15 07:11:11.501] [debug] Writing virtual memory chunk: virt=0x116e0, size=0x1, process=0
[2025-07-15 07:11:11.501] [info] Translating virtual to physical: virt=0x116e0, process=0
[2025-07-15 07:11:11.501] [debug] Marked page dirty: virt=0x116e0
[2025-07-15 07:11:11.501] [debug] Wrote virtual memory chunk: virt=0x116e0, size=0x1
[2025-07-15 07:11:11.501] [debug] Writing virtual memory chunk: virt=0x106e0, size=0x10, process=0
[2025-07-15 07:11:11.501] [info] Translating virtual to physical: virt=0x106e0, process=0
[2025-07-15 07:11:11.502] [debug] Marked page dirty: virt=0x106e0
[2025-07-15 07:11:11.502] [debug] Wrote virtual memory chunk: virt=0x106e0, size=0x10
[2025-07-15 07:11:11.502] [debug] Writing virtual memory chunk: virt=0x116f0, size=0x1, process=0
[2025-07-15 07:11:11.502] [info] Translating virtual to physical: virt=0x116f0, process=0
[2025-07-15 07:11:11.502] [debug] Marked page dirty: virt=0x116f0
[2025-07-15 07:11:11.502] [debug] Wrote virtual memory chunk: virt=0x116f0, size=0x1
[2025-07-15 07:11:11.502] [debug] Writing virtual memory chunk: virt=0x106f0, size=0x10, process=0
[2025-07-15 07:11:11.502] [info] Translating virtual to physical: virt=0x106f0, process=0
[2025-07-15 07:11:11.502] [debug] Marked page dirty: virt=0x106f0
[2025-07-15 07:11:11.502] [debug] Wrote virtual memory chunk: virt=0x106f0, size=0x10
[2025-07-15 07:11:11.502] [debug] Writing virtual memory chunk: virt=0x11700, size=0x1, process=0
[2025-07-15 07:11:11.502] [info] Translating virtual to physical: virt=0x11700, process=0
[2025-07-15 07:11:11.502] [debug] Marked page dirty: virt=0x11700
[2025-07-15 07:11:11.502] [debug] Wrote virtual memory chunk: virt=0x11700, size=0x1
[2025-07-15 07:11:11.502] [debug] Writing virtual memory chunk: virt=0x10700, size=0x10, process=0
[2025-07-15 07:11:11.502] [info] Translating virtual to physical: virt=0x10700, process=0
[2025-07-15 07:11:11.502] [debug] Marked page dirty: virt=0x10700
[2025-07-15 07:11:11.503] [debug] Wrote virtual memory chunk: virt=0x10700, size=0x10
[2025-07-15 07:11:11.503] [debug] Writing virtual memory chunk: virt=0x11710, size=0x1, process=0
[2025-07-15 07:11:11.503] [info] Translating virtual to physical: virt=0x11710, process=0
[2025-07-15 07:11:11.503] [debug] Marked page dirty: virt=0x11710
[2025-07-15 07:11:11.503] [debug] Wrote virtual memory chunk: virt=0x11710, size=0x1
[2025-07-15 07:11:11.503] [debug] Writing virtual memory chunk: virt=0x10710, size=0x10, process=0
[2025-07-15 07:11:11.503] [info] Translating virtual to physical: virt=0x10710, process=0
[2025-07-15 07:11:11.503] [debug] Marked page dirty: virt=0x10710
[2025-07-15 07:11:11.503] [debug] Wrote virtual memory chunk: virt=0x10710, size=0x10
[2025-07-15 07:11:11.503] [debug] Writing virtual memory chunk: virt=0x11720, size=0x1, process=0
[2025-07-15 07:11:11.503] [info] Translating virtual to physical: virt=0x11720, process=0
[2025-07-15 07:11:11.503] [debug] Marked page dirty: virt=0x11720
[2025-07-15 07:11:11.503] [debug] Wrote virtual memory chunk: virt=0x11720, size=0x1
[2025-07-15 07:11:11.503] [debug] Writing virtual memory chunk: virt=0x10720, size=0x10, process=0
[2025-07-15 07:11:11.503] [info] Translating virtual to physical: virt=0x10720, process=0
[2025-07-15 07:11:11.504] [debug] Marked page dirty: virt=0x10720
[2025-07-15 07:11:11.504] [debug] Wrote virtual memory chunk: virt=0x10720, size=0x10
[2025-07-15 07:11:11.504] [debug] Writing virtual memory chunk: virt=0x11730, size=0x1, process=0
[2025-07-15 07:11:11.504] [info] Translating virtual to physical: virt=0x11730, process=0
[2025-07-15 07:11:11.504] [debug] Marked page dirty: virt=0x11730
[2025-07-15 07:11:11.504] [debug] Wrote virtual memory chunk: virt=0x11730, size=0x1
[2025-07-15 07:11:11.504] [debug] Writing virtual memory chunk: virt=0x10730, size=0x10, process=0
[2025-07-15 07:11:11.504] [info] Translating virtual to physical: virt=0x10730, process=0
[2025-07-15 07:11:11.504] [debug] Marked page dirty: virt=0x10730
[2025-07-15 07:11:11.504] [debug] Wrote virtual memory chunk: virt=0x10730, size=0x10
[2025-07-15 07:11:11.504] [debug] Writing virtual memory chunk: virt=0x11740, size=0x1, process=0
[2025-07-15 07:11:11.504] [info] Translating virtual to physical: virt=0x11740, process=0
[2025-07-15 07:11:11.504] [debug] Marked page dirty: virt=0x11740
[2025-07-15 07:11:11.504] [debug] Wrote virtual memory chunk: virt=0x11740, size=0x1
[2025-07-15 07:11:11.504] [debug] Writing virtual memory chunk: virt=0x10740, size=0x10, process=0
[2025-07-15 07:11:11.504] [info] Translating virtual to physical: virt=0x10740, process=0
[2025-07-15 07:11:11.504] [debug] Marked page dirty: virt=0x10740
[2025-07-15 07:11:11.504] [debug] Wrote virtual memory chunk: virt=0x10740, size=0x10
[2025-07-15 07:11:11.505] [debug] Writing virtual memory chunk: virt=0x11750, size=0x1, process=0
[2025-07-15 07:11:11.505] [info] Translating virtual to physical: virt=0x11750, process=0
[2025-07-15 07:11:11.505] [debug] Marked page dirty: virt=0x11750
[2025-07-15 07:11:11.505] [debug] Wrote virtual memory chunk: virt=0x11750, size=0x1
[2025-07-15 07:11:11.505] [debug] Writing virtual memory chunk: virt=0x10750, size=0x10, process=0
[2025-07-15 07:11:11.505] [info] Translating virtual to physical: virt=0x10750, process=0
[2025-07-15 07:11:11.505] [debug] Marked page dirty: virt=0x10750
[2025-07-15 07:11:11.505] [debug] Wrote virtual memory chunk: virt=0x10750, size=0x10
[2025-07-15 07:11:11.505] [debug] Writing virtual memory chunk: virt=0x11760, size=0x1, process=0
[2025-07-15 07:11:11.505] [info] Translating virtual to physical: virt=0x11760, process=0
[2025-07-15 07:11:11.505] [debug] Marked page dirty: virt=0x11760
[2025-07-15 07:11:11.505] [debug] Wrote virtual memory chunk: virt=0x11760, size=0x1
[2025-07-15 07:11:11.505] [debug] Writing virtual memory chunk: virt=0x10760, size=0x10, process=0
[2025-07-15 07:11:11.506] [info] Translating virtual to physical: virt=0x10760, process=0
[2025-07-15 07:11:11.506] [debug] Marked page dirty: virt=0x10760
[2025-07-15 07:11:11.506] [debug] Wrote virtual memory chunk: virt=0x10760, size=0x10
[2025-07-15 07:11:11.506] [debug] Writing virtual memory chunk: virt=0x11770, size=0x1, process=0
[2025-07-15 07:11:11.506] [info] Translating virtual to physical: virt=0x11770, process=0
[2025-07-15 07:11:11.506] [debug] Marked page dirty: virt=0x11770
[2025-07-15 07:11:11.506] [debug] Wrote virtual memory chunk: virt=0x11770, size=0x1
[2025-07-15 07:11:11.506] [debug] Writing virtual memory chunk: virt=0x10770, size=0x10, process=0
[2025-07-15 07:11:11.506] [info] Translating virtual to physical: virt=0x10770, process=0
[2025-07-15 07:11:11.506] [debug] Marked page dirty: virt=0x10770
[2025-07-15 07:11:11.506] [debug] Wrote virtual memory chunk: virt=0x10770, size=0x10
[2025-07-15 07:11:11.506] [debug] Writing virtual memory chunk: virt=0x11780, size=0x1, process=0
[2025-07-15 07:11:11.506] [info] Translating virtual to physical: virt=0x11780, process=0
[2025-07-15 07:11:11.506] [debug] Marked page dirty: virt=0x11780
[2025-07-15 07:11:11.506] [debug] Wrote virtual memory chunk: virt=0x11780, size=0x1
[2025-07-15 07:11:11.506] [debug] Writing virtual memory chunk: virt=0x10780, size=0x10, process=0
[2025-07-15 07:11:11.507] [info] Translating virtual to physical: virt=0x10780, process=0
[2025-07-15 07:11:11.507] [debug] Marked page dirty: virt=0x10780
[2025-07-15 07:11:11.507] [debug] Wrote virtual memory chunk: virt=0x10780, size=0x10
[2025-07-15 07:11:11.507] [debug] Writing virtual memory chunk: virt=0x11790, size=0x1, process=0
[2025-07-15 07:11:11.507] [info] Translating virtual to physical: virt=0x11790, process=0
[2025-07-15 07:11:11.507] [debug] Marked page dirty: virt=0x11790
[2025-07-15 07:11:11.507] [debug] Wrote virtual memory chunk: virt=0x11790, size=0x1
[2025-07-15 07:11:11.507] [debug] Writing virtual memory chunk: virt=0x10790, size=0x10, process=0
[2025-07-15 07:11:11.507] [info] Translating virtual to physical: virt=0x10790, process=0
[2025-07-15 07:11:11.507] [debug] Marked page dirty: virt=0x10790
[2025-07-15 07:11:11.507] [debug] Wrote virtual memory chunk: virt=0x10790, size=0x10
[2025-07-15 07:11:11.507] [debug] Writing virtual memory chunk: virt=0x117a0, size=0x1, process=0
[2025-07-15 07:11:11.507] [info] Translating virtual to physical: virt=0x117a0, process=0
[2025-07-15 07:11:11.507] [debug] Marked page dirty: virt=0x117a0
[2025-07-15 07:11:11.507] [debug] Wrote virtual memory chunk: virt=0x117a0, size=0x1
[2025-07-15 07:11:11.507] [debug] Writing virtual memory chunk: virt=0x107a0, size=0x10, process=0
[2025-07-15 07:11:11.507] [info] Translating virtual to physical: virt=0x107a0, process=0
[2025-07-15 07:11:11.508] [debug] Marked page dirty: virt=0x107a0
[2025-07-15 07:11:11.508] [debug] Wrote virtual memory chunk: virt=0x107a0, size=0x10
[2025-07-15 07:11:11.508] [debug] Writing virtual memory chunk: virt=0x117b0, size=0x1, process=0
[2025-07-15 07:11:11.508] [info] Translating virtual to physical: virt=0x117b0, process=0
[2025-07-15 07:11:11.508] [debug] Marked page dirty: virt=0x117b0
[2025-07-15 07:11:11.508] [debug] Wrote virtual memory chunk: virt=0x117b0, size=0x1
[2025-07-15 07:11:11.508] [debug] Writing virtual memory chunk: virt=0x107b0, size=0x10, process=0
[2025-07-15 07:11:11.508] [info] Translating virtual to physical: virt=0x107b0, process=0
[2025-07-15 07:11:11.508] [debug] Marked page dirty: virt=0x107b0
[2025-07-15 07:11:11.508] [debug] Wrote virtual memory chunk: virt=0x107b0, size=0x10
[2025-07-15 07:11:11.508] [debug] Writing virtual memory chunk: virt=0x117c0, size=0x1, process=0
[2025-07-15 07:11:11.508] [info] Translating virtual to physical: virt=0x117c0, process=0
[2025-07-15 07:11:11.508] [debug] Marked page dirty: virt=0x117c0
[2025-07-15 07:11:11.508] [debug] Wrote virtual memory chunk: virt=0x117c0, size=0x1
[2025-07-15 07:11:11.508] [debug] Writing virtual memory chunk: virt=0x107c0, size=0x10, process=0
[2025-07-15 07:11:11.509] [info] Translating virtual to physical: virt=0x107c0, process=0
[2025-07-15 07:11:11.509] [debug] Marked page dirty: virt=0x107c0
[2025-07-15 07:11:11.509] [debug] Wrote virtual memory chunk: virt=0x107c0, size=0x10
[2025-07-15 07:11:11.509] [debug] Writing virtual memory chunk: virt=0x117d0, size=0x1, process=0
[2025-07-15 07:11:11.509] [info] Translating virtual to physical: virt=0x117d0, process=0
[2025-07-15 07:11:11.509] [debug] Marked page dirty: virt=0x117d0
[2025-07-15 07:11:11.509] [debug] Wrote virtual memory chunk: virt=0x117d0, size=0x1
[2025-07-15 07:11:11.509] [debug] Writing virtual memory chunk: virt=0x107d0, size=0x10, process=0
[2025-07-15 07:11:11.509] [info] Translating virtual to physical: virt=0x107d0, process=0
[2025-07-15 07:11:11.509] [debug] Marked page dirty: virt=0x107d0
[2025-07-15 07:11:11.509] [debug] Wrote virtual memory chunk: virt=0x107d0, size=0x10
[2025-07-15 07:11:11.509] [debug] Writing virtual memory chunk: virt=0x117e0, size=0x1, process=0
[2025-07-15 07:11:11.509] [info] Translating virtual to physical: virt=0x117e0, process=0
[2025-07-15 07:11:11.509] [debug] Marked page dirty: virt=0x117e0
[2025-07-15 07:11:11.509] [debug] Wrote virtual memory chunk: virt=0x117e0, size=0x1
[2025-07-15 07:11:11.510] [debug] Writing virtual memory chunk: virt=0x107e0, size=0x10, process=0
[2025-07-15 07:11:11.510] [info] Translating virtual to physical: virt=0x107e0, process=0
[2025-07-15 07:11:11.510] [debug] Marked page dirty: virt=0x107e0
[2025-07-15 07:11:11.510] [debug] Wrote virtual memory chunk: virt=0x107e0, size=0x10
[2025-07-15 07:11:11.510] [debug] Writing virtual memory chunk: virt=0x117f0, size=0x1, process=0
[2025-07-15 07:11:11.510] [info] Translating virtual to physical: virt=0x117f0, process=0
[2025-07-15 07:11:11.510] [debug] Marked page dirty: virt=0x117f0
[2025-07-15 07:11:11.512] [debug] Wrote virtual memory chunk: virt=0x117f0, size=0x1
[2025-07-15 07:11:11.512] [debug] Writing virtual memory chunk: virt=0x107f0, size=0x10, process=0
[2025-07-15 07:11:11.512] [info] Translating virtual to physical: virt=0x107f0, process=0
[2025-07-15 07:11:11.512] [debug] Marked page dirty: virt=0x107f0
[2025-07-15 07:11:11.512] [debug] Wrote virtual memory chunk: virt=0x107f0, size=0x10
[2025-07-15 07:11:11.512] [debug] Writing virtual memory chunk: virt=0x11800, size=0x1, process=0
[2025-07-15 07:11:11.512] [info] Translating virtual to physical: virt=0x11800, process=0
[2025-07-15 07:11:11.512] [debug] Marked page dirty: virt=0x11800
[2025-07-15 07:11:11.512] [debug] Wrote virtual memory chunk: virt=0x11800, size=0x1
[2025-07-15 07:11:11.512] [debug] Writing virtual memory chunk: virt=0x10800, size=0x10, process=0
[2025-07-15 07:11:11.512] [info] Translating virtual to physical: virt=0x10800, process=0
[2025-07-15 07:11:11.512] [debug] Marked page dirty: virt=0x10800
[2025-07-15 07:11:11.512] [debug] Wrote virtual memory chunk: virt=0x10800, size=0x10
[2025-07-15 07:11:11.512] [debug] Writing virtual memory chunk: virt=0x11810, size=0x1, process=0
[2025-07-15 07:11:11.513] [info] Translating virtual to physical: virt=0x11810, process=0
[2025-07-15 07:11:11.513] [debug] Marked page dirty: virt=0x11810
[2025-07-15 07:11:11.513] [debug] Wrote virtual memory chunk: virt=0x11810, size=0x1
[2025-07-15 07:11:11.513] [debug] Writing virtual memory chunk: virt=0x10810, size=0x10, process=0
[2025-07-15 07:11:11.513] [info] Translating virtual to physical: virt=0x10810, process=0
[2025-07-15 07:11:11.513] [debug] Marked page dirty: virt=0x10810
[2025-07-15 07:11:11.513] [debug] Wrote virtual memory chunk: virt=0x10810, size=0x10
[2025-07-15 07:11:11.513] [debug] Writing virtual memory chunk: virt=0x11820, size=0x1, process=0
[2025-07-15 07:11:11.513] [info] Translating virtual to physical: virt=0x11820, process=0
[2025-07-15 07:11:11.513] [debug] Marked page dirty: virt=0x11820
[2025-07-15 07:11:11.513] [debug] Wrote virtual memory chunk: virt=0x11820, size=0x1
[2025-07-15 07:11:11.513] [debug] Writing virtual memory chunk: virt=0x10820, size=0x10, process=0
[2025-07-15 07:11:11.513] [info] Translating virtual to physical: virt=0x10820, process=0
[2025-07-15 07:11:11.513] [debug] Marked page dirty: virt=0x10820
[2025-07-15 07:11:11.513] [debug] Wrote virtual memory chunk: virt=0x10820, size=0x10
[2025-07-15 07:11:11.513] [debug] Writing virtual memory chunk: virt=0x11830, size=0x1, process=0
[2025-07-15 07:11:11.514] [info] Translating virtual to physical: virt=0x11830, process=0
[2025-07-15 07:11:11.514] [debug] Marked page dirty: virt=0x11830
[2025-07-15 07:11:11.514] [debug] Wrote virtual memory chunk: virt=0x11830, size=0x1
[2025-07-15 07:11:11.514] [debug] Writing virtual memory chunk: virt=0x10830, size=0x10, process=0
[2025-07-15 07:11:11.514] [info] Translating virtual to physical: virt=0x10830, process=0
[2025-07-15 07:11:11.514] [debug] Marked page dirty: virt=0x10830
[2025-07-15 07:11:11.514] [debug] Wrote virtual memory chunk: virt=0x10830, size=0x10
[2025-07-15 07:11:11.514] [debug] Writing virtual memory chunk: virt=0x11840, size=0x1, process=0
[2025-07-15 07:11:11.514] [info] Translating virtual to physical: virt=0x11840, process=0
[2025-07-15 07:11:11.514] [debug] Marked page dirty: virt=0x11840
[2025-07-15 07:11:11.514] [debug] Wrote virtual memory chunk: virt=0x11840, size=0x1
[2025-07-15 07:11:11.514] [debug] Writing virtual memory chunk: virt=0x10840, size=0x10, process=0
[2025-07-15 07:11:11.514] [info] Translating virtual to physical: virt=0x10840, process=0
[2025-07-15 07:11:11.514] [debug] Marked page dirty: virt=0x10840
[2025-07-15 07:11:11.514] [debug] Wrote virtual memory chunk: virt=0x10840, size=0x10
[2025-07-15 07:11:11.514] [debug] Writing virtual memory chunk: virt=0x11850, size=0x1, process=0
[2025-07-15 07:11:11.514] [info] Translating virtual to physical: virt=0x11850, process=0
[2025-07-15 07:11:11.514] [debug] Marked page dirty: virt=0x11850
[2025-07-15 07:11:11.515] [debug] Wrote virtual memory chunk: virt=0x11850, size=0x1
[2025-07-15 07:11:11.515] [debug] Writing virtual memory chunk: virt=0x10850, size=0x10, process=0
[2025-07-15 07:11:11.515] [info] Translating virtual to physical: virt=0x10850, process=0
[2025-07-15 07:11:11.515] [debug] Marked page dirty: virt=0x10850
[2025-07-15 07:11:11.515] [debug] Wrote virtual memory chunk: virt=0x10850, size=0x10
[2025-07-15 07:11:11.515] [debug] Writing virtual memory chunk: virt=0x11860, size=0x1, process=0
[2025-07-15 07:11:11.515] [info] Translating virtual to physical: virt=0x11860, process=0
[2025-07-15 07:11:11.515] [debug] Marked page dirty: virt=0x11860
[2025-07-15 07:11:11.515] [debug] Wrote virtual memory chunk: virt=0x11860, size=0x1
[2025-07-15 07:11:11.515] [debug] Writing virtual memory chunk: virt=0x10860, size=0x10, process=0
[2025-07-15 07:11:11.515] [info] Translating virtual to physical: virt=0x10860, process=0
[2025-07-15 07:11:11.515] [debug] Marked page dirty: virt=0x10860
[2025-07-15 07:11:11.515] [debug] Wrote virtual memory chunk: virt=0x10860, size=0x10
[2025-07-15 07:11:11.515] [debug] Writing virtual memory chunk: virt=0x11870, size=0x1, process=0
[2025-07-15 07:11:11.515] [info] Translating virtual to physical: virt=0x11870, process=0
[2025-07-15 07:11:11.515] [debug] Marked page dirty: virt=0x11870
[2025-07-15 07:11:11.516] [debug] Wrote virtual memory chunk: virt=0x11870, size=0x1
[2025-07-15 07:11:11.516] [debug] Writing virtual memory chunk: virt=0x10870, size=0x10, process=0
[2025-07-15 07:11:11.516] [info] Translating virtual to physical: virt=0x10870, process=0
[2025-07-15 07:11:11.516] [debug] Marked page dirty: virt=0x10870
[2025-07-15 07:11:11.516] [debug] Wrote virtual memory chunk: virt=0x10870, size=0x10
[2025-07-15 07:11:11.516] [debug] Writing virtual memory chunk: virt=0x11880, size=0x1, process=0
[2025-07-15 07:11:11.516] [info] Translating virtual to physical: virt=0x11880, process=0
[2025-07-15 07:11:11.516] [debug] Marked page dirty: virt=0x11880
[2025-07-15 07:11:11.516] [debug] Wrote virtual memory chunk: virt=0x11880, size=0x1
[2025-07-15 07:11:11.516] [debug] Writing virtual memory chunk: virt=0x10880, size=0x10, process=0
[2025-07-15 07:11:11.516] [info] Translating virtual to physical: virt=0x10880, process=0
[2025-07-15 07:11:11.516] [debug] Marked page dirty: virt=0x10880
[2025-07-15 07:11:11.516] [debug] Wrote virtual memory chunk: virt=0x10880, size=0x10
[2025-07-15 07:11:11.516] [debug] Writing virtual memory chunk: virt=0x11890, size=0x1, process=0
[2025-07-15 07:11:11.516] [info] Translating virtual to physical: virt=0x11890, process=0
[2025-07-15 07:11:11.516] [debug] Marked page dirty: virt=0x11890
[2025-07-15 07:11:11.517] [debug] Wrote virtual memory chunk: virt=0x11890, size=0x1
[2025-07-15 07:11:11.517] [debug] Writing virtual memory chunk: virt=0x10890, size=0x10, process=0
[2025-07-15 07:11:11.517] [info] Translating virtual to physical: virt=0x10890, process=0
[2025-07-15 07:11:11.517] [debug] Marked page dirty: virt=0x10890
[2025-07-15 07:11:11.517] [debug] Wrote virtual memory chunk: virt=0x10890, size=0x10
[2025-07-15 07:11:11.517] [debug] Writing virtual memory chunk: virt=0x118a0, size=0x1, process=0
[2025-07-15 07:11:11.517] [info] Translating virtual to physical: virt=0x118a0, process=0
[2025-07-15 07:11:11.517] [debug] Marked page dirty: virt=0x118a0
[2025-07-15 07:11:11.517] [debug] Wrote virtual memory chunk: virt=0x118a0, size=0x1
[2025-07-15 07:11:11.517] [debug] Writing virtual memory chunk: virt=0x108a0, size=0x10, process=0
[2025-07-15 07:11:11.517] [info] Translating virtual to physical: virt=0x108a0, process=0
[2025-07-15 07:11:11.517] [debug] Marked page dirty: virt=0x108a0
[2025-07-15 07:11:11.517] [debug] Wrote virtual memory chunk: virt=0x108a0, size=0x10
[2025-07-15 07:11:11.518] [debug] Writing virtual memory chunk: virt=0x118b0, size=0x1, process=0
[2025-07-15 07:11:11.518] [info] Translating virtual to physical: virt=0x118b0, process=0
[2025-07-15 07:11:11.518] [debug] Marked page dirty: virt=0x118b0
[2025-07-15 07:11:11.518] [debug] Wrote virtual memory chunk: virt=0x118b0, size=0x1
[2025-07-15 07:11:11.518] [debug] Writing virtual memory chunk: virt=0x108b0, size=0x10, process=0
[2025-07-15 07:11:11.518] [info] Translating virtual to physical: virt=0x108b0, process=0
[2025-07-15 07:11:11.518] [debug] Marked page dirty: virt=0x108b0
[2025-07-15 07:11:11.518] [debug] Wrote virtual memory chunk: virt=0x108b0, size=0x10
[2025-07-15 07:11:11.518] [debug] Writing virtual memory chunk: virt=0x118c0, size=0x1, process=0
[2025-07-15 07:11:11.518] [info] Translating virtual to physical: virt=0x118c0, process=0
[2025-07-15 07:11:11.518] [debug] Marked page dirty: virt=0x118c0
[2025-07-15 07:11:11.518] [debug] Wrote virtual memory chunk: virt=0x118c0, size=0x1
[2025-07-15 07:11:11.518] [debug] Writing virtual memory chunk: virt=0x108c0, size=0x10, process=0
[2025-07-15 07:11:11.518] [info] Translating virtual to physical: virt=0x108c0, process=0
[2025-07-15 07:11:11.518] [debug] Marked page dirty: virt=0x108c0
[2025-07-15 07:11:11.519] [debug] Wrote virtual memory chunk: virt=0x108c0, size=0x10
[2025-07-15 07:11:11.519] [debug] Writing virtual memory chunk: virt=0x118d0, size=0x1, process=0
[2025-07-15 07:11:11.519] [info] Translating virtual to physical: virt=0x118d0, process=0
[2025-07-15 07:11:11.519] [debug] Marked page dirty: virt=0x118d0
[2025-07-15 07:11:11.519] [debug] Wrote virtual memory chunk: virt=0x118d0, size=0x1
[2025-07-15 07:11:11.519] [debug] Writing virtual memory chunk: virt=0x108d0, size=0x10, process=0
[2025-07-15 07:11:11.519] [info] Translating virtual to physical: virt=0x108d0, process=0
[2025-07-15 07:11:11.519] [debug] Marked page dirty: virt=0x108d0
[2025-07-15 07:11:11.519] [debug] Wrote virtual memory chunk: virt=0x108d0, size=0x10
[2025-07-15 07:11:11.519] [debug] Writing virtual memory chunk: virt=0x118e0, size=0x1, process=0
[2025-07-15 07:11:11.519] [info] Translating virtual to physical: virt=0x118e0, process=0
[2025-07-15 07:11:11.519] [debug] Marked page dirty: virt=0x118e0
[2025-07-15 07:11:11.519] [debug] Wrote virtual memory chunk: virt=0x118e0, size=0x1
[2025-07-15 07:11:11.520] [debug] Writing virtual memory chunk: virt=0x108e0, size=0x10, process=0
[2025-07-15 07:11:11.520] [info] Translating virtual to physical: virt=0x108e0, process=0
[2025-07-15 07:11:11.520] [debug] Marked page dirty: virt=0x108e0
[2025-07-15 07:11:11.520] [debug] Wrote virtual memory chunk: virt=0x108e0, size=0x10
[2025-07-15 07:11:11.520] [debug] Writing virtual memory chunk: virt=0x118f0, size=0x1, process=0
[2025-07-15 07:11:11.520] [info] Translating virtual to physical: virt=0x118f0, process=0
[2025-07-15 07:11:11.520] [debug] Marked page dirty: virt=0x118f0
[2025-07-15 07:11:11.520] [debug] Wrote virtual memory chunk: virt=0x118f0, size=0x1
[2025-07-15 07:11:11.520] [debug] Writing virtual memory chunk: virt=0x108f0, size=0x10, process=0
[2025-07-15 07:11:11.520] [info] Translating virtual to physical: virt=0x108f0, process=0
[2025-07-15 07:11:11.520] [debug] Marked page dirty: virt=0x108f0
[2025-07-15 07:11:11.520] [debug] Wrote virtual memory chunk: virt=0x108f0, size=0x10
[2025-07-15 07:11:11.520] [debug] Writing virtual memory chunk: virt=0x11900, size=0x1, process=0
[2025-07-15 07:11:11.520] [info] Translating virtual to physical: virt=0x11900, process=0
[2025-07-15 07:11:11.520] [debug] Marked page dirty: virt=0x11900
[2025-07-15 07:11:11.520] [debug] Wrote virtual memory chunk: virt=0x11900, size=0x1
[2025-07-15 07:11:11.521] [debug] Writing virtual memory chunk: virt=0x10900, size=0x10, process=0
[2025-07-15 07:11:11.521] [info] Translating virtual to physical: virt=0x10900, process=0
[2025-07-15 07:11:11.521] [debug] Marked page dirty: virt=0x10900
[2025-07-15 07:11:11.521] [debug] Wrote virtual memory chunk: virt=0x10900, size=0x10
[2025-07-15 07:11:11.521] [debug] Writing virtual memory chunk: virt=0x11910, size=0x1, process=0
[2025-07-15 07:11:11.521] [info] Translating virtual to physical: virt=0x11910, process=0
[2025-07-15 07:11:11.521] [debug] Marked page dirty: virt=0x11910
[2025-07-15 07:11:11.521] [debug] Wrote virtual memory chunk: virt=0x11910, size=0x1
[2025-07-15 07:11:11.521] [debug] Writing virtual memory chunk: virt=0x10910, size=0x10, process=0
[2025-07-15 07:11:11.521] [info] Translating virtual to physical: virt=0x10910, process=0
[2025-07-15 07:11:11.521] [debug] Marked page dirty: virt=0x10910
[2025-07-15 07:11:11.521] [debug] Wrote virtual memory chunk: virt=0x10910, size=0x10
[2025-07-15 07:11:11.521] [debug] Writing virtual memory chunk: virt=0x11920, size=0x1, process=0
[2025-07-15 07:11:11.521] [info] Translating virtual to physical: virt=0x11920, process=0
[2025-07-15 07:11:11.521] [debug] Marked page dirty: virt=0x11920
[2025-07-15 07:11:11.521] [debug] Wrote virtual memory chunk: virt=0x11920, size=0x1
[2025-07-15 07:11:11.521] [debug] Writing virtual memory chunk: virt=0x10920, size=0x10, process=0
[2025-07-15 07:11:11.522] [info] Translating virtual to physical: virt=0x10920, process=0
[2025-07-15 07:11:11.522] [debug] Marked page dirty: virt=0x10920
[2025-07-15 07:11:11.522] [debug] Wrote virtual memory chunk: virt=0x10920, size=0x10
[2025-07-15 07:11:11.522] [debug] Writing virtual memory chunk: virt=0x11930, size=0x1, process=0
[2025-07-15 07:11:11.522] [info] Translating virtual to physical: virt=0x11930, process=0
[2025-07-15 07:11:11.522] [debug] Marked page dirty: virt=0x11930
[2025-07-15 07:11:11.522] [debug] Wrote virtual memory chunk: virt=0x11930, size=0x1
[2025-07-15 07:11:11.522] [debug] Writing virtual memory chunk: virt=0x10930, size=0x10, process=0
[2025-07-15 07:11:11.522] [info] Translating virtual to physical: virt=0x10930, process=0
[2025-07-15 07:11:11.522] [debug] Marked page dirty: virt=0x10930
[2025-07-15 07:11:11.523] [debug] Wrote virtual memory chunk: virt=0x10930, size=0x10
[2025-07-15 07:11:11.523] [debug] Writing virtual memory chunk: virt=0x11940, size=0x1, process=0
[2025-07-15 07:11:11.523] [info] Translating virtual to physical: virt=0x11940, process=0
[2025-07-15 07:11:11.523] [debug] Marked page dirty: virt=0x11940
[2025-07-15 07:11:11.523] [debug] Wrote virtual memory chunk: virt=0x11940, size=0x1
[2025-07-15 07:11:11.523] [debug] Writing virtual memory chunk: virt=0x10940, size=0x10, process=0
[2025-07-15 07:11:11.523] [info] Translating virtual to physical: virt=0x10940, process=0
[2025-07-15 07:11:11.523] [debug] Marked page dirty: virt=0x10940
[2025-07-15 07:11:11.523] [debug] Wrote virtual memory chunk: virt=0x10940, size=0x10
[2025-07-15 07:11:11.523] [debug] Writing virtual memory chunk: virt=0x11950, size=0x1, process=0
[2025-07-15 07:11:11.523] [info] Translating virtual to physical: virt=0x11950, process=0
[2025-07-15 07:11:11.523] [debug] Marked page dirty: virt=0x11950
[2025-07-15 07:11:11.523] [debug] Wrote virtual memory chunk: virt=0x11950, size=0x1
[2025-07-15 07:11:11.523] [debug] Writing virtual memory chunk: virt=0x10950, size=0x10, process=0
[2025-07-15 07:11:11.523] [info] Translating virtual to physical: virt=0x10950, process=0
[2025-07-15 07:11:11.524] [debug] Marked page dirty: virt=0x10950
[2025-07-15 07:11:11.524] [debug] Wrote virtual memory chunk: virt=0x10950, size=0x10
[2025-07-15 07:11:11.524] [debug] Writing virtual memory chunk: virt=0x11960, size=0x1, process=0
[2025-07-15 07:11:11.524] [info] Translating virtual to physical: virt=0x11960, process=0
[2025-07-15 07:11:11.524] [debug] Marked page dirty: virt=0x11960
[2025-07-15 07:11:11.524] [debug] Wrote virtual memory chunk: virt=0x11960, size=0x1
[2025-07-15 07:11:11.524] [debug] Writing virtual memory chunk: virt=0x10960, size=0x10, process=0
[2025-07-15 07:11:11.524] [info] Translating virtual to physical: virt=0x10960, process=0
[2025-07-15 07:11:11.524] [debug] Marked page dirty: virt=0x10960
[2025-07-15 07:11:11.524] [debug] Wrote virtual memory chunk: virt=0x10960, size=0x10
[2025-07-15 07:11:11.524] [info] Processed descriptor 150
[2025-07-15 07:11:11.524] [debug] Writing virtual memory chunk: virt=0x11970, size=0x1, process=0
[2025-07-15 07:11:11.524] [info] Translating virtual to physical: virt=0x11970, process=0
[2025-07-15 07:11:11.524] [debug] Marked page dirty: virt=0x11970
[2025-07-15 07:11:11.524] [debug] Wrote virtual memory chunk: virt=0x11970, size=0x1
[2025-07-15 07:11:11.524] [debug] Writing virtual memory chunk: virt=0x10970, size=0x10, process=0
[2025-07-15 07:11:11.524] [info] Translating virtual to physical: virt=0x10970, process=0
[2025-07-15 07:11:11.525] [debug] Marked page dirty: virt=0x10970
[2025-07-15 07:11:11.525] [debug] Wrote virtual memory chunk: virt=0x10970, size=0x10
[2025-07-15 07:11:11.525] [debug] Writing virtual memory chunk: virt=0x11980, size=0x1, process=0
[2025-07-15 07:11:11.525] [info] Translating virtual to physical: virt=0x11980, process=0
[2025-07-15 07:11:11.525] [debug] Marked page dirty: virt=0x11980
[2025-07-15 07:11:11.525] [debug] Wrote virtual memory chunk: virt=0x11980, size=0x1
[2025-07-15 07:11:11.525] [debug] Writing virtual memory chunk: virt=0x10980, size=0x10, process=0
[2025-07-15 07:11:11.525] [info] Translating virtual to physical: virt=0x10980, process=0
[2025-07-15 07:11:11.525] [debug] Marked page dirty: virt=0x10980
[2025-07-15 07:11:11.525] [debug] Wrote virtual memory chunk: virt=0x10980, size=0x10
[2025-07-15 07:11:11.525] [debug] Writing virtual memory chunk: virt=0x11990, size=0x1, process=0
[2025-07-15 07:11:11.525] [info] Translating virtual to physical: virt=0x11990, process=0
[2025-07-15 07:11:11.526] [debug] Marked page dirty: virt=0x11990
[2025-07-15 07:11:11.527] [debug] Wrote virtual memory chunk: virt=0x11990, size=0x1
[2025-07-15 07:11:11.527] [debug] Writing virtual memory chunk: virt=0x10990, size=0x10, process=0
[2025-07-15 07:11:11.527] [info] Translating virtual to physical: virt=0x10990, process=0
[2025-07-15 07:11:11.527] [debug] Marked page dirty: virt=0x10990
[2025-07-15 07:11:11.527] [debug] Wrote virtual memory chunk: virt=0x10990, size=0x10
[2025-07-15 07:11:11.527] [debug] Writing virtual memory chunk: virt=0x119a0, size=0x1, process=0
[2025-07-15 07:11:11.527] [info] Translating virtual to physical: virt=0x119a0, process=0
[2025-07-15 07:11:11.527] [debug] Marked page dirty: virt=0x119a0
[2025-07-15 07:11:11.527] [debug] Wrote virtual memory chunk: virt=0x119a0, size=0x1
[2025-07-15 07:11:11.528] [debug] Writing virtual memory chunk: virt=0x109a0, size=0x10, process=0
[2025-07-15 07:11:11.528] [info] Translating virtual to physical: virt=0x109a0, process=0
[2025-07-15 07:11:11.528] [debug] Marked page dirty: virt=0x109a0
[2025-07-15 07:11:11.528] [debug] Wrote virtual memory chunk: virt=0x109a0, size=0x10
[2025-07-15 07:11:11.528] [debug] Writing virtual memory chunk: virt=0x119b0, size=0x1, process=0
[2025-07-15 07:11:11.528] [info] Translating virtual to physical: virt=0x119b0, process=0
[2025-07-15 07:11:11.528] [debug] Marked page dirty: virt=0x119b0
[2025-07-15 07:11:11.528] [debug] Wrote virtual memory chunk: virt=0x119b0, size=0x1
[2025-07-15 07:11:11.528] [debug] Writing virtual memory chunk: virt=0x109b0, size=0x10, process=0
[2025-07-15 07:11:11.528] [info] Translating virtual to physical: virt=0x109b0, process=0
[2025-07-15 07:11:11.528] [debug] Marked page dirty: virt=0x109b0
[2025-07-15 07:11:11.528] [debug] Wrote virtual memory chunk: virt=0x109b0, size=0x10
[2025-07-15 07:11:11.528] [debug] Writing virtual memory chunk: virt=0x119c0, size=0x1, process=0
[2025-07-15 07:11:11.528] [info] Translating virtual to physical: virt=0x119c0, process=0
[2025-07-15 07:11:11.529] [debug] Marked page dirty: virt=0x119c0
[2025-07-15 07:11:11.529] [debug] Wrote virtual memory chunk: virt=0x119c0, size=0x1
[2025-07-15 07:11:11.529] [debug] Writing virtual memory chunk: virt=0x109c0, size=0x10, process=0
[2025-07-15 07:11:11.529] [info] Translating virtual to physical: virt=0x109c0, process=0
[2025-07-15 07:11:11.529] [debug] Marked page dirty: virt=0x109c0
[2025-07-15 07:11:11.529] [debug] Wrote virtual memory chunk: virt=0x109c0, size=0x10
[2025-07-15 07:11:11.529] [debug] Writing virtual memory chunk: virt=0x119d0, size=0x1, process=0
[2025-07-15 07:11:11.529] [info] Translating virtual to physical: virt=0x119d0, process=0
[2025-07-15 07:11:11.529] [debug] Marked page dirty: virt=0x119d0
[2025-07-15 07:11:11.529] [debug] Wrote virtual memory chunk: virt=0x119d0, size=0x1
[2025-07-15 07:11:11.529] [debug] Writing virtual memory chunk: virt=0x109d0, size=0x10, process=0
[2025-07-15 07:11:11.529] [info] Translating virtual to physical: virt=0x109d0, process=0
[2025-07-15 07:11:11.529] [debug] Marked page dirty: virt=0x109d0
[2025-07-15 07:11:11.529] [debug] Wrote virtual memory chunk: virt=0x109d0, size=0x10
[2025-07-15 07:11:11.529] [debug] Writing virtual memory chunk: virt=0x119e0, size=0x1, process=0
[2025-07-15 07:11:11.530] [info] Translating virtual to physical: virt=0x119e0, process=0
[2025-07-15 07:11:11.530] [debug] Marked page dirty: virt=0x119e0
[2025-07-15 07:11:11.530] [debug] Wrote virtual memory chunk: virt=0x119e0, size=0x1
[2025-07-15 07:11:11.530] [debug] Writing virtual memory chunk: virt=0x109e0, size=0x10, process=0
[2025-07-15 07:11:11.530] [info] Translating virtual to physical: virt=0x109e0, process=0
[2025-07-15 07:11:11.530] [debug] Marked page dirty: virt=0x109e0
[2025-07-15 07:11:11.530] [debug] Wrote virtual memory chunk: virt=0x109e0, size=0x10
[2025-07-15 07:11:11.530] [debug] Writing virtual memory chunk: virt=0x119f0, size=0x1, process=0
[2025-07-15 07:11:11.530] [info] Translating virtual to physical: virt=0x119f0, process=0
[2025-07-15 07:11:11.530] [debug] Marked page dirty: virt=0x119f0
[2025-07-15 07:11:11.530] [debug] Wrote virtual memory chunk: virt=0x119f0, size=0x1
[2025-07-15 07:11:11.530] [debug] Writing virtual memory chunk: virt=0x109f0, size=0x10, process=0
[2025-07-15 07:11:11.530] [info] Translating virtual to physical: virt=0x109f0, process=0
[2025-07-15 07:11:11.530] [debug] Marked page dirty: virt=0x109f0
[2025-07-15 07:11:11.530] [debug] Wrote virtual memory chunk: virt=0x109f0, size=0x10
[2025-07-15 07:11:11.530] [debug] Writing virtual memory chunk: virt=0x11a00, size=0x1, process=0
[2025-07-15 07:11:11.530] [info] Translating virtual to physical: virt=0x11a00, process=0
[2025-07-15 07:11:11.531] [debug] Marked page dirty: virt=0x11a00
[2025-07-15 07:11:11.531] [debug] Wrote virtual memory chunk: virt=0x11a00, size=0x1
[2025-07-15 07:11:11.531] [debug] Writing virtual memory chunk: virt=0x10a00, size=0x10, process=0
[2025-07-15 07:11:11.531] [info] Translating virtual to physical: virt=0x10a00, process=0
[2025-07-15 07:11:11.531] [debug] Marked page dirty: virt=0x10a00
[2025-07-15 07:11:11.531] [debug] Wrote virtual memory chunk: virt=0x10a00, size=0x10
[2025-07-15 07:11:11.531] [debug] Writing virtual memory chunk: virt=0x11a10, size=0x1, process=0
[2025-07-15 07:11:11.531] [info] Translating virtual to physical: virt=0x11a10, process=0
[2025-07-15 07:11:11.531] [debug] Marked page dirty: virt=0x11a10
[2025-07-15 07:11:11.531] [debug] Wrote virtual memory chunk: virt=0x11a10, size=0x1
[2025-07-15 07:11:11.531] [debug] Writing virtual memory chunk: virt=0x10a10, size=0x10, process=0
[2025-07-15 07:11:11.531] [info] Translating virtual to physical: virt=0x10a10, process=0
[2025-07-15 07:11:11.531] [debug] Marked page dirty: virt=0x10a10
[2025-07-15 07:11:11.531] [debug] Wrote virtual memory chunk: virt=0x10a10, size=0x10
[2025-07-15 07:11:11.532] [debug] Writing virtual memory chunk: virt=0x11a20, size=0x1, process=0
[2025-07-15 07:11:11.532] [info] Translating virtual to physical: virt=0x11a20, process=0
[2025-07-15 07:11:11.532] [debug] Marked page dirty: virt=0x11a20
[2025-07-15 07:11:11.532] [debug] Wrote virtual memory chunk: virt=0x11a20, size=0x1
[2025-07-15 07:11:11.532] [debug] Writing virtual memory chunk: virt=0x10a20, size=0x10, process=0
[2025-07-15 07:11:11.532] [info] Translating virtual to physical: virt=0x10a20, process=0
[2025-07-15 07:11:11.532] [debug] Marked page dirty: virt=0x10a20
[2025-07-15 07:11:11.532] [debug] Wrote virtual memory chunk: virt=0x10a20, size=0x10
[2025-07-15 07:11:11.532] [debug] Writing virtual memory chunk: virt=0x11a30, size=0x1, process=0
[2025-07-15 07:11:11.532] [info] Translating virtual to physical: virt=0x11a30, process=0
[2025-07-15 07:11:11.532] [debug] Marked page dirty: virt=0x11a30
[2025-07-15 07:11:11.532] [debug] Wrote virtual memory chunk: virt=0x11a30, size=0x1
[2025-07-15 07:11:11.532] [debug] Writing virtual memory chunk: virt=0x10a30, size=0x10, process=0
[2025-07-15 07:11:11.532] [info] Translating virtual to physical: virt=0x10a30, process=0
[2025-07-15 07:11:11.532] [debug] Marked page dirty: virt=0x10a30
[2025-07-15 07:11:11.533] [debug] Wrote virtual memory chunk: virt=0x10a30, size=0x10
[2025-07-15 07:11:11.533] [debug] Writing virtual memory chunk: virt=0x11a40, size=0x1, process=0
[2025-07-15 07:11:11.533] [info] Translating virtual to physical: virt=0x11a40, process=0
[2025-07-15 07:11:11.533] [debug] Marked page dirty: virt=0x11a40
[2025-07-15 07:11:11.533] [debug] Wrote virtual memory chunk: virt=0x11a40, size=0x1
[2025-07-15 07:11:11.533] [debug] Writing virtual memory chunk: virt=0x10a40, size=0x10, process=0
[2025-07-15 07:11:11.533] [info] Translating virtual to physical: virt=0x10a40, process=0
[2025-07-15 07:11:11.533] [debug] Marked page dirty: virt=0x10a40
[2025-07-15 07:11:11.533] [debug] Wrote virtual memory chunk: virt=0x10a40, size=0x10
[2025-07-15 07:11:11.533] [debug] Writing virtual memory chunk: virt=0x11a50, size=0x1, process=0
[2025-07-15 07:11:11.533] [info] Translating virtual to physical: virt=0x11a50, process=0
[2025-07-15 07:11:11.533] [debug] Marked page dirty: virt=0x11a50
[2025-07-15 07:11:11.533] [debug] Wrote virtual memory chunk: virt=0x11a50, size=0x1
[2025-07-15 07:11:11.533] [debug] Writing virtual memory chunk: virt=0x10a50, size=0x10, process=0
[2025-07-15 07:11:11.533] [info] Translating virtual to physical: virt=0x10a50, process=0
[2025-07-15 07:11:11.533] [debug] Marked page dirty: virt=0x10a50
[2025-07-15 07:11:11.534] [debug] Wrote virtual memory chunk: virt=0x10a50, size=0x10
[2025-07-15 07:11:11.534] [debug] Writing virtual memory chunk: virt=0x11a60, size=0x1, process=0
[2025-07-15 07:11:11.534] [info] Translating virtual to physical: virt=0x11a60, process=0
[2025-07-15 07:11:11.534] [debug] Marked page dirty: virt=0x11a60
[2025-07-15 07:11:11.534] [debug] Wrote virtual memory chunk: virt=0x11a60, size=0x1
[2025-07-15 07:11:11.534] [debug] Writing virtual memory chunk: virt=0x10a60, size=0x10, process=0
[2025-07-15 07:11:11.534] [info] Translating virtual to physical: virt=0x10a60, process=0
[2025-07-15 07:11:11.534] [debug] Marked page dirty: virt=0x10a60
[2025-07-15 07:11:11.534] [debug] Wrote virtual memory chunk: virt=0x10a60, size=0x10
[2025-07-15 07:11:11.534] [debug] Writing virtual memory chunk: virt=0x11a70, size=0x1, process=0
[2025-07-15 07:11:11.534] [info] Translating virtual to physical: virt=0x11a70, process=0
[2025-07-15 07:11:11.534] [debug] Marked page dirty: virt=0x11a70
[2025-07-15 07:11:11.534] [debug] Wrote virtual memory chunk: virt=0x11a70, size=0x1
[2025-07-15 07:11:11.534] [debug] Writing virtual memory chunk: virt=0x10a70, size=0x10, process=0
[2025-07-15 07:11:11.535] [info] Translating virtual to physical: virt=0x10a70, process=0
[2025-07-15 07:11:11.535] [debug] Marked page dirty: virt=0x10a70
[2025-07-15 07:11:11.535] [debug] Wrote virtual memory chunk: virt=0x10a70, size=0x10
[2025-07-15 07:11:11.535] [debug] Writing virtual memory chunk: virt=0x11a80, size=0x1, process=0
[2025-07-15 07:11:11.535] [info] Translating virtual to physical: virt=0x11a80, process=0
[2025-07-15 07:11:11.535] [debug] Marked page dirty: virt=0x11a80
[2025-07-15 07:11:11.535] [debug] Wrote virtual memory chunk: virt=0x11a80, size=0x1
[2025-07-15 07:11:11.535] [debug] Writing virtual memory chunk: virt=0x10a80, size=0x10, process=0
[2025-07-15 07:11:11.535] [info] Translating virtual to physical: virt=0x10a80, process=0
[2025-07-15 07:11:11.535] [debug] Marked page dirty: virt=0x10a80
[2025-07-15 07:11:11.535] [debug] Wrote virtual memory chunk: virt=0x10a80, size=0x10
[2025-07-15 07:11:11.535] [debug] Writing virtual memory chunk: virt=0x11a90, size=0x1, process=0
[2025-07-15 07:11:11.535] [info] Translating virtual to physical: virt=0x11a90, process=0
[2025-07-15 07:11:11.535] [debug] Marked page dirty: virt=0x11a90
[2025-07-15 07:11:11.535] [debug] Wrote virtual memory chunk: virt=0x11a90, size=0x1
[2025-07-15 07:11:11.535] [debug] Writing virtual memory chunk: virt=0x10a90, size=0x10, process=0
[2025-07-15 07:11:11.536] [info] Translating virtual to physical: virt=0x10a90, process=0
[2025-07-15 07:11:11.536] [debug] Marked page dirty: virt=0x10a90
[2025-07-15 07:11:11.536] [debug] Wrote virtual memory chunk: virt=0x10a90, size=0x10
[2025-07-15 07:11:11.536] [debug] Writing virtual memory chunk: virt=0x11aa0, size=0x1, process=0
[2025-07-15 07:11:11.536] [info] Translating virtual to physical: virt=0x11aa0, process=0
[2025-07-15 07:11:11.536] [debug] Marked page dirty: virt=0x11aa0
[2025-07-15 07:11:11.536] [debug] Wrote virtual memory chunk: virt=0x11aa0, size=0x1
[2025-07-15 07:11:11.536] [debug] Writing virtual memory chunk: virt=0x10aa0, size=0x10, process=0
[2025-07-15 07:11:11.536] [info] Translating virtual to physical: virt=0x10aa0, process=0
[2025-07-15 07:11:11.536] [debug] Marked page dirty: virt=0x10aa0
[2025-07-15 07:11:11.536] [debug] Wrote virtual memory chunk: virt=0x10aa0, size=0x10
[2025-07-15 07:11:11.536] [debug] Writing virtual memory chunk: virt=0x11ab0, size=0x1, process=0
[2025-07-15 07:11:11.536] [info] Translating virtual to physical: virt=0x11ab0, process=0
[2025-07-15 07:11:11.536] [debug] Marked page dirty: virt=0x11ab0
[2025-07-15 07:11:11.536] [debug] Wrote virtual memory chunk: virt=0x11ab0, size=0x1
[2025-07-15 07:11:11.537] [debug] Writing virtual memory chunk: virt=0x10ab0, size=0x10, process=0
[2025-07-15 07:11:11.537] [info] Translating virtual to physical: virt=0x10ab0, process=0
[2025-07-15 07:11:11.537] [debug] Marked page dirty: virt=0x10ab0
[2025-07-15 07:11:11.537] [debug] Wrote virtual memory chunk: virt=0x10ab0, size=0x10
[2025-07-15 07:11:11.537] [debug] Writing virtual memory chunk: virt=0x11ac0, size=0x1, process=0
[2025-07-15 07:11:11.537] [info] Translating virtual to physical: virt=0x11ac0, process=0
[2025-07-15 07:11:11.537] [debug] Marked page dirty: virt=0x11ac0
[2025-07-15 07:11:11.537] [debug] Wrote virtual memory chunk: virt=0x11ac0, size=0x1
[2025-07-15 07:11:11.537] [debug] Writing virtual memory chunk: virt=0x10ac0, size=0x10, process=0
[2025-07-15 07:11:11.537] [info] Translating virtual to physical: virt=0x10ac0, process=0
[2025-07-15 07:11:11.537] [debug] Marked page dirty: virt=0x10ac0
[2025-07-15 07:11:11.537] [debug] Wrote virtual memory chunk: virt=0x10ac0, size=0x10
[2025-07-15 07:11:11.537] [debug] Writing virtual memory chunk: virt=0x11ad0, size=0x1, process=0
[2025-07-15 07:11:11.537] [info] Translating virtual to physical: virt=0x11ad0, process=0
[2025-07-15 07:11:11.538] [debug] Marked page dirty: virt=0x11ad0
[2025-07-15 07:11:11.538] [debug] Wrote virtual memory chunk: virt=0x11ad0, size=0x1
[2025-07-15 07:11:11.538] [debug] Writing virtual memory chunk: virt=0x10ad0, size=0x10, process=0
[2025-07-15 07:11:11.538] [info] Translating virtual to physical: virt=0x10ad0, process=0
[2025-07-15 07:11:11.538] [debug] Marked page dirty: virt=0x10ad0
[2025-07-15 07:11:11.538] [debug] Wrote virtual memory chunk: virt=0x10ad0, size=0x10
[2025-07-15 07:11:11.538] [debug] Writing virtual memory chunk: virt=0x11ae0, size=0x1, process=0
[2025-07-15 07:11:11.538] [info] Translating virtual to physical: virt=0x11ae0, process=0
[2025-07-15 07:11:11.538] [debug] Marked page dirty: virt=0x11ae0
[2025-07-15 07:11:11.538] [debug] Wrote virtual memory chunk: virt=0x11ae0, size=0x1
[2025-07-15 07:11:11.538] [debug] Writing virtual memory chunk: virt=0x10ae0, size=0x10, process=0
[2025-07-15 07:11:11.538] [info] Translating virtual to physical: virt=0x10ae0, process=0
[2025-07-15 07:11:11.538] [debug] Marked page dirty: virt=0x10ae0
[2025-07-15 07:11:11.539] [debug] Wrote virtual memory chunk: virt=0x10ae0, size=0x10
[2025-07-15 07:11:11.539] [debug] Writing virtual memory chunk: virt=0x11af0, size=0x1, process=0
[2025-07-15 07:11:11.539] [info] Translating virtual to physical: virt=0x11af0, process=0
[2025-07-15 07:11:11.539] [debug] Marked page dirty: virt=0x11af0
[2025-07-15 07:11:11.539] [debug] Wrote virtual memory chunk: virt=0x11af0, size=0x1
[2025-07-15 07:11:11.539] [debug] Writing virtual memory chunk: virt=0x10af0, size=0x10, process=0
[2025-07-15 07:11:11.539] [info] Translating virtual to physical: virt=0x10af0, process=0
[2025-07-15 07:11:11.539] [debug] Marked page dirty: virt=0x10af0
[2025-07-15 07:11:11.539] [debug] Wrote virtual memory chunk: virt=0x10af0, size=0x10
[2025-07-15 07:11:11.539] [debug] Writing virtual memory chunk: virt=0x11b00, size=0x1, process=0
[2025-07-15 07:11:11.539] [info] Translating virtual to physical: virt=0x11b00, process=0
[2025-07-15 07:11:11.539] [debug] Marked page dirty: virt=0x11b00
[2025-07-15 07:11:11.539] [debug] Wrote virtual memory chunk: virt=0x11b00, size=0x1
[2025-07-15 07:11:11.539] [debug] Writing virtual memory chunk: virt=0x10b00, size=0x10, process=0
[2025-07-15 07:11:11.539] [info] Translating virtual to physical: virt=0x10b00, process=0
[2025-07-15 07:11:11.540] [debug] Marked page dirty: virt=0x10b00
[2025-07-15 07:11:11.540] [debug] Wrote virtual memory chunk: virt=0x10b00, size=0x10
[2025-07-15 07:11:11.540] [debug] Writing virtual memory chunk: virt=0x11b10, size=0x1, process=0
[2025-07-15 07:11:11.540] [info] Translating virtual to physical: virt=0x11b10, process=0
[2025-07-15 07:11:11.540] [debug] Marked page dirty: virt=0x11b10
[2025-07-15 07:11:11.540] [debug] Wrote virtual memory chunk: virt=0x11b10, size=0x1
[2025-07-15 07:11:11.540] [debug] Writing virtual memory chunk: virt=0x10b10, size=0x10, process=0
[2025-07-15 07:11:11.540] [info] Translating virtual to physical: virt=0x10b10, process=0
[2025-07-15 07:11:11.540] [debug] Marked page dirty: virt=0x10b10
[2025-07-15 07:11:11.540] [debug] Wrote virtual memory chunk: virt=0x10b10, size=0x10
[2025-07-15 07:11:11.540] [debug] Writing virtual memory chunk: virt=0x11b20, size=0x1, process=0
[2025-07-15 07:11:11.540] [info] Translating virtual to physical: virt=0x11b20, process=0
[2025-07-15 07:11:11.540] [debug] Marked page dirty: virt=0x11b20
[2025-07-15 07:11:11.540] [debug] Wrote virtual memory chunk: virt=0x11b20, size=0x1
[2025-07-15 07:11:11.540] [debug] Writing virtual memory chunk: virt=0x10b20, size=0x10, process=0
[2025-07-15 07:11:11.540] [info] Translating virtual to physical: virt=0x10b20, process=0
[2025-07-15 07:11:11.540] [debug] Marked page dirty: virt=0x10b20
[2025-07-15 07:11:11.540] [debug] Wrote virtual memory chunk: virt=0x10b20, size=0x10
[2025-07-15 07:11:11.540] [debug] Writing virtual memory chunk: virt=0x11b30, size=0x1, process=0
[2025-07-15 07:11:11.541] [info] Translating virtual to physical: virt=0x11b30, process=0
[2025-07-15 07:11:11.541] [debug] Marked page dirty: virt=0x11b30
[2025-07-15 07:11:11.541] [debug] Wrote virtual memory chunk: virt=0x11b30, size=0x1
[2025-07-15 07:11:11.541] [debug] Writing virtual memory chunk: virt=0x10b30, size=0x10, process=0
[2025-07-15 07:11:11.541] [info] Translating virtual to physical: virt=0x10b30, process=0
[2025-07-15 07:11:11.541] [debug] Marked page dirty: virt=0x10b30
[2025-07-15 07:11:11.541] [debug] Wrote virtual memory chunk: virt=0x10b30, size=0x10
[2025-07-15 07:11:11.541] [debug] Writing virtual memory chunk: virt=0x11b40, size=0x1, process=0
[2025-07-15 07:11:11.542] [info] Translating virtual to physical: virt=0x11b40, process=0
[2025-07-15 07:11:11.543] [debug] Marked page dirty: virt=0x11b40
[2025-07-15 07:11:11.543] [debug] Wrote virtual memory chunk: virt=0x11b40, size=0x1
[2025-07-15 07:11:11.543] [debug] Writing virtual memory chunk: virt=0x10b40, size=0x10, process=0
[2025-07-15 07:11:11.543] [info] Translating virtual to physical: virt=0x10b40, process=0
[2025-07-15 07:11:11.543] [debug] Marked page dirty: virt=0x10b40
[2025-07-15 07:11:11.543] [debug] Wrote virtual memory chunk: virt=0x10b40, size=0x10
[2025-07-15 07:11:11.543] [debug] Writing virtual memory chunk: virt=0x11b50, size=0x1, process=0
[2025-07-15 07:11:11.543] [info] Translating virtual to physical: virt=0x11b50, process=0
[2025-07-15 07:11:11.543] [debug] Marked page dirty: virt=0x11b50
[2025-07-15 07:11:11.543] [debug] Wrote virtual memory chunk: virt=0x11b50, size=0x1
[2025-07-15 07:11:11.543] [debug] Writing virtual memory chunk: virt=0x10b50, size=0x10, process=0
[2025-07-15 07:11:11.543] [info] Translating virtual to physical: virt=0x10b50, process=0
[2025-07-15 07:11:11.543] [debug] Marked page dirty: virt=0x10b50
[2025-07-15 07:11:11.543] [debug] Wrote virtual memory chunk: virt=0x10b50, size=0x10
[2025-07-15 07:11:11.543] [debug] Writing virtual memory chunk: virt=0x11b60, size=0x1, process=0
[2025-07-15 07:11:11.543] [info] Translating virtual to physical: virt=0x11b60, process=0
[2025-07-15 07:11:11.544] [debug] Marked page dirty: virt=0x11b60
[2025-07-15 07:11:11.544] [debug] Wrote virtual memory chunk: virt=0x11b60, size=0x1
[2025-07-15 07:11:11.544] [debug] Writing virtual memory chunk: virt=0x10b60, size=0x10, process=0
[2025-07-15 07:11:11.544] [info] Translating virtual to physical: virt=0x10b60, process=0
[2025-07-15 07:11:11.544] [debug] Marked page dirty: virt=0x10b60
[2025-07-15 07:11:11.544] [debug] Wrote virtual memory chunk: virt=0x10b60, size=0x10
[2025-07-15 07:11:11.544] [debug] Writing virtual memory chunk: virt=0x11b70, size=0x1, process=0
[2025-07-15 07:11:11.544] [info] Translating virtual to physical: virt=0x11b70, process=0
[2025-07-15 07:11:11.544] [debug] Marked page dirty: virt=0x11b70
[2025-07-15 07:11:11.544] [debug] Wrote virtual memory chunk: virt=0x11b70, size=0x1
[2025-07-15 07:11:11.544] [debug] Writing virtual memory chunk: virt=0x10b70, size=0x10, process=0
[2025-07-15 07:11:11.544] [info] Translating virtual to physical: virt=0x10b70, process=0
[2025-07-15 07:11:11.544] [debug] Marked page dirty: virt=0x10b70
[2025-07-15 07:11:11.545] [debug] Wrote virtual memory chunk: virt=0x10b70, size=0x10
[2025-07-15 07:11:11.545] [debug] Writing virtual memory chunk: virt=0x11b80, size=0x1, process=0
[2025-07-15 07:11:11.545] [info] Translating virtual to physical: virt=0x11b80, process=0
[2025-07-15 07:11:11.545] [debug] Marked page dirty: virt=0x11b80
[2025-07-15 07:11:11.545] [debug] Wrote virtual memory chunk: virt=0x11b80, size=0x1
[2025-07-15 07:11:11.545] [debug] Writing virtual memory chunk: virt=0x10b80, size=0x10, process=0
[2025-07-15 07:11:11.545] [info] Translating virtual to physical: virt=0x10b80, process=0
[2025-07-15 07:11:11.545] [debug] Marked page dirty: virt=0x10b80
[2025-07-15 07:11:11.545] [debug] Wrote virtual memory chunk: virt=0x10b80, size=0x10
[2025-07-15 07:11:11.545] [debug] Writing virtual memory chunk: virt=0x11b90, size=0x1, process=0
[2025-07-15 07:11:11.545] [info] Translating virtual to physical: virt=0x11b90, process=0
[2025-07-15 07:11:11.545] [debug] Marked page dirty: virt=0x11b90
[2025-07-15 07:11:11.545] [debug] Wrote virtual memory chunk: virt=0x11b90, size=0x1
[2025-07-15 07:11:11.545] [debug] Writing virtual memory chunk: virt=0x10b90, size=0x10, process=0
[2025-07-15 07:11:11.545] [info] Translating virtual to physical: virt=0x10b90, process=0
[2025-07-15 07:11:11.545] [debug] Marked page dirty: virt=0x10b90
[2025-07-15 07:11:11.546] [debug] Wrote virtual memory chunk: virt=0x10b90, size=0x10
[2025-07-15 07:11:11.546] [debug] Writing virtual memory chunk: virt=0x11ba0, size=0x1, process=0
[2025-07-15 07:11:11.546] [info] Translating virtual to physical: virt=0x11ba0, process=0
[2025-07-15 07:11:11.546] [debug] Marked page dirty: virt=0x11ba0
[2025-07-15 07:11:11.546] [debug] Wrote virtual memory chunk: virt=0x11ba0, size=0x1
[2025-07-15 07:11:11.546] [debug] Writing virtual memory chunk: virt=0x10ba0, size=0x10, process=0
[2025-07-15 07:11:11.546] [info] Translating virtual to physical: virt=0x10ba0, process=0
[2025-07-15 07:11:11.546] [debug] Marked page dirty: virt=0x10ba0
[2025-07-15 07:11:11.546] [debug] Wrote virtual memory chunk: virt=0x10ba0, size=0x10
[2025-07-15 07:11:11.546] [debug] Writing virtual memory chunk: virt=0x11bb0, size=0x1, process=0
[2025-07-15 07:11:11.546] [info] Translating virtual to physical: virt=0x11bb0, process=0
[2025-07-15 07:11:11.546] [debug] Marked page dirty: virt=0x11bb0
[2025-07-15 07:11:11.546] [debug] Wrote virtual memory chunk: virt=0x11bb0, size=0x1
[2025-07-15 07:11:11.546] [debug] Writing virtual memory chunk: virt=0x10bb0, size=0x10, process=0
[2025-07-15 07:11:11.546] [info] Translating virtual to physical: virt=0x10bb0, process=0
[2025-07-15 07:11:11.546] [debug] Marked page dirty: virt=0x10bb0
[2025-07-15 07:11:11.546] [debug] Wrote virtual memory chunk: virt=0x10bb0, size=0x10
[2025-07-15 07:11:11.547] [debug] Writing virtual memory chunk: virt=0x11bc0, size=0x1, process=0
[2025-07-15 07:11:11.547] [info] Translating virtual to physical: virt=0x11bc0, process=0
[2025-07-15 07:11:11.547] [debug] Marked page dirty: virt=0x11bc0
[2025-07-15 07:11:11.547] [debug] Wrote virtual memory chunk: virt=0x11bc0, size=0x1
[2025-07-15 07:11:11.547] [debug] Writing virtual memory chunk: virt=0x10bc0, size=0x10, process=0
[2025-07-15 07:11:11.547] [info] Translating virtual to physical: virt=0x10bc0, process=0
[2025-07-15 07:11:11.547] [debug] Marked page dirty: virt=0x10bc0
[2025-07-15 07:11:11.547] [debug] Wrote virtual memory chunk: virt=0x10bc0, size=0x10
[2025-07-15 07:11:11.547] [debug] Writing virtual memory chunk: virt=0x11bd0, size=0x1, process=0
[2025-07-15 07:11:11.547] [info] Translating virtual to physical: virt=0x11bd0, process=0
[2025-07-15 07:11:11.547] [debug] Marked page dirty: virt=0x11bd0
[2025-07-15 07:11:11.547] [debug] Wrote virtual memory chunk: virt=0x11bd0, size=0x1
[2025-07-15 07:11:11.547] [debug] Writing virtual memory chunk: virt=0x10bd0, size=0x10, process=0
[2025-07-15 07:11:11.547] [info] Translating virtual to physical: virt=0x10bd0, process=0
[2025-07-15 07:11:11.547] [debug] Marked page dirty: virt=0x10bd0
[2025-07-15 07:11:11.547] [debug] Wrote virtual memory chunk: virt=0x10bd0, size=0x10
[2025-07-15 07:11:11.548] [debug] Writing virtual memory chunk: virt=0x11be0, size=0x1, process=0
[2025-07-15 07:11:11.548] [info] Translating virtual to physical: virt=0x11be0, process=0
[2025-07-15 07:11:11.548] [debug] Marked page dirty: virt=0x11be0
[2025-07-15 07:11:11.548] [debug] Wrote virtual memory chunk: virt=0x11be0, size=0x1
[2025-07-15 07:11:11.548] [debug] Writing virtual memory chunk: virt=0x10be0, size=0x10, process=0
[2025-07-15 07:11:11.548] [info] Translating virtual to physical: virt=0x10be0, process=0
[2025-07-15 07:11:11.548] [debug] Marked page dirty: virt=0x10be0
[2025-07-15 07:11:11.548] [debug] Wrote virtual memory chunk: virt=0x10be0, size=0x10
[2025-07-15 07:11:11.548] [debug] Writing virtual memory chunk: virt=0x11bf0, size=0x1, process=0
[2025-07-15 07:11:11.548] [info] Translating virtual to physical: virt=0x11bf0, process=0
[2025-07-15 07:11:11.548] [debug] Marked page dirty: virt=0x11bf0
[2025-07-15 07:11:11.548] [debug] Wrote virtual memory chunk: virt=0x11bf0, size=0x1
[2025-07-15 07:11:11.548] [debug] Writing virtual memory chunk: virt=0x10bf0, size=0x10, process=0
[2025-07-15 07:11:11.548] [info] Translating virtual to physical: virt=0x10bf0, process=0
[2025-07-15 07:11:11.548] [debug] Marked page dirty: virt=0x10bf0
[2025-07-15 07:11:11.549] [debug] Wrote virtual memory chunk: virt=0x10bf0, size=0x10
[2025-07-15 07:11:11.549] [debug] Writing virtual memory chunk: virt=0x11c00, size=0x1, process=0
[2025-07-15 07:11:11.549] [info] Translating virtual to physical: virt=0x11c00, process=0
[2025-07-15 07:11:11.549] [debug] Marked page dirty: virt=0x11c00
[2025-07-15 07:11:11.549] [debug] Wrote virtual memory chunk: virt=0x11c00, size=0x1
[2025-07-15 07:11:11.549] [debug] Writing virtual memory chunk: virt=0x10c00, size=0x10, process=0
[2025-07-15 07:11:11.549] [info] Translating virtual to physical: virt=0x10c00, process=0
[2025-07-15 07:11:11.549] [debug] Marked page dirty: virt=0x10c00
[2025-07-15 07:11:11.549] [debug] Wrote virtual memory chunk: virt=0x10c00, size=0x10
[2025-07-15 07:11:11.549] [debug] Writing virtual memory chunk: virt=0x11c10, size=0x1, process=0
[2025-07-15 07:11:11.549] [info] Translating virtual to physical: virt=0x11c10, process=0
[2025-07-15 07:11:11.549] [debug] Marked page dirty: virt=0x11c10
[2025-07-15 07:11:11.549] [debug] Wrote virtual memory chunk: virt=0x11c10, size=0x1
[2025-07-15 07:11:11.549] [debug] Writing virtual memory chunk: virt=0x10c10, size=0x10, process=0
[2025-07-15 07:11:11.549] [info] Translating virtual to physical: virt=0x10c10, process=0
[2025-07-15 07:11:11.550] [debug] Marked page dirty: virt=0x10c10
[2025-07-15 07:11:11.550] [debug] Wrote virtual memory chunk: virt=0x10c10, size=0x10
[2025-07-15 07:11:11.550] [debug] Writing virtual memory chunk: virt=0x11c20, size=0x1, process=0
[2025-07-15 07:11:11.550] [info] Translating virtual to physical: virt=0x11c20, process=0
[2025-07-15 07:11:11.550] [debug] Marked page dirty: virt=0x11c20
[2025-07-15 07:11:11.550] [debug] Wrote virtual memory chunk: virt=0x11c20, size=0x1
[2025-07-15 07:11:11.550] [debug] Writing virtual memory chunk: virt=0x10c20, size=0x10, process=0
[2025-07-15 07:11:11.550] [info] Translating virtual to physical: virt=0x10c20, process=0
[2025-07-15 07:11:11.550] [debug] Marked page dirty: virt=0x10c20
[2025-07-15 07:11:11.550] [debug] Wrote virtual memory chunk: virt=0x10c20, size=0x10
[2025-07-15 07:11:11.550] [debug] Writing virtual memory chunk: virt=0x11c30, size=0x1, process=0
[2025-07-15 07:11:11.550] [info] Translating virtual to physical: virt=0x11c30, process=0
[2025-07-15 07:11:11.550] [debug] Marked page dirty: virt=0x11c30
[2025-07-15 07:11:11.551] [debug] Wrote virtual memory chunk: virt=0x11c30, size=0x1
[2025-07-15 07:11:11.551] [debug] Writing virtual memory chunk: virt=0x10c30, size=0x10, process=0
[2025-07-15 07:11:11.551] [info] Translating virtual to physical: virt=0x10c30, process=0
[2025-07-15 07:11:11.551] [debug] Marked page dirty: virt=0x10c30
[2025-07-15 07:11:11.551] [debug] Wrote virtual memory chunk: virt=0x10c30, size=0x10
[2025-07-15 07:11:11.551] [debug] Writing virtual memory chunk: virt=0x11c40, size=0x1, process=0
[2025-07-15 07:11:11.551] [info] Translating virtual to physical: virt=0x11c40, process=0
[2025-07-15 07:11:11.551] [debug] Marked page dirty: virt=0x11c40
[2025-07-15 07:11:11.551] [debug] Wrote virtual memory chunk: virt=0x11c40, size=0x1
[2025-07-15 07:11:11.551] [debug] Writing virtual memory chunk: virt=0x10c40, size=0x10, process=0
[2025-07-15 07:11:11.551] [info] Translating virtual to physical: virt=0x10c40, process=0
[2025-07-15 07:11:11.551] [debug] Marked page dirty: virt=0x10c40
[2025-07-15 07:11:11.551] [debug] Wrote virtual memory chunk: virt=0x10c40, size=0x10
[2025-07-15 07:11:11.551] [debug] Writing virtual memory chunk: virt=0x11c50, size=0x1, process=0
[2025-07-15 07:11:11.551] [info] Translating virtual to physical: virt=0x11c50, process=0
[2025-07-15 07:11:11.552] [debug] Marked page dirty: virt=0x11c50
[2025-07-15 07:11:11.552] [debug] Wrote virtual memory chunk: virt=0x11c50, size=0x1
[2025-07-15 07:11:11.552] [debug] Writing virtual memory chunk: virt=0x10c50, size=0x10, process=0
[2025-07-15 07:11:11.552] [info] Translating virtual to physical: virt=0x10c50, process=0
[2025-07-15 07:11:11.552] [debug] Marked page dirty: virt=0x10c50
[2025-07-15 07:11:11.552] [debug] Wrote virtual memory chunk: virt=0x10c50, size=0x10
[2025-07-15 07:11:11.552] [debug] Writing virtual memory chunk: virt=0x11c60, size=0x1, process=0
[2025-07-15 07:11:11.552] [info] Translating virtual to physical: virt=0x11c60, process=0
[2025-07-15 07:11:11.552] [debug] Marked page dirty: virt=0x11c60
[2025-07-15 07:11:11.552] [debug] Wrote virtual memory chunk: virt=0x11c60, size=0x1
[2025-07-15 07:11:11.552] [debug] Writing virtual memory chunk: virt=0x10c60, size=0x10, process=0
[2025-07-15 07:11:11.552] [info] Translating virtual to physical: virt=0x10c60, process=0
[2025-07-15 07:11:11.552] [debug] Marked page dirty: virt=0x10c60
[2025-07-15 07:11:11.552] [debug] Wrote virtual memory chunk: virt=0x10c60, size=0x10
[2025-07-15 07:11:11.552] [debug] Writing virtual memory chunk: virt=0x11c70, size=0x1, process=0
[2025-07-15 07:11:11.552] [info] Translating virtual to physical: virt=0x11c70, process=0
[2025-07-15 07:11:11.552] [debug] Marked page dirty: virt=0x11c70
[2025-07-15 07:11:11.552] [debug] Wrote virtual memory chunk: virt=0x11c70, size=0x1
[2025-07-15 07:11:11.552] [debug] Writing virtual memory chunk: virt=0x10c70, size=0x10, process=0
[2025-07-15 07:11:11.553] [info] Translating virtual to physical: virt=0x10c70, process=0
[2025-07-15 07:11:11.553] [debug] Marked page dirty: virt=0x10c70
[2025-07-15 07:11:11.553] [debug] Wrote virtual memory chunk: virt=0x10c70, size=0x10
[2025-07-15 07:11:11.553] [debug] Writing virtual memory chunk: virt=0x11c80, size=0x1, process=0
[2025-07-15 07:11:11.553] [info] Translating virtual to physical: virt=0x11c80, process=0
[2025-07-15 07:11:11.553] [debug] Marked page dirty: virt=0x11c80
[2025-07-15 07:11:11.553] [debug] Wrote virtual memory chunk: virt=0x11c80, size=0x1
[2025-07-15 07:11:11.553] [debug] Writing virtual memory chunk: virt=0x10c80, size=0x10, process=0
[2025-07-15 07:11:11.553] [info] Translating virtual to physical: virt=0x10c80, process=0
[2025-07-15 07:11:11.553] [debug] Marked page dirty: virt=0x10c80
[2025-07-15 07:11:11.553] [debug] Wrote virtual memory chunk: virt=0x10c80, size=0x10
[2025-07-15 07:11:11.553] [info] Processed descriptor 200
[2025-07-15 07:11:11.553] [debug] Writing virtual memory chunk: virt=0x11c90, size=0x1, process=0
[2025-07-15 07:11:11.554] [info] Translating virtual to physical: virt=0x11c90, process=0
[2025-07-15 07:11:11.554] [debug] Marked page dirty: virt=0x11c90
[2025-07-15 07:11:11.554] [debug] Wrote virtual memory chunk: virt=0x11c90, size=0x1
[2025-07-15 07:11:11.554] [debug] Writing virtual memory chunk: virt=0x10c90, size=0x10, process=0
[2025-07-15 07:11:11.554] [info] Translating virtual to physical: virt=0x10c90, process=0
[2025-07-15 07:11:11.554] [debug] Marked page dirty: virt=0x10c90
[2025-07-15 07:11:11.554] [debug] Wrote virtual memory chunk: virt=0x10c90, size=0x10
[2025-07-15 07:11:11.554] [debug] Writing virtual memory chunk: virt=0x11ca0, size=0x1, process=0
[2025-07-15 07:11:11.554] [info] Translating virtual to physical: virt=0x11ca0, process=0
[2025-07-15 07:11:11.554] [debug] Marked page dirty: virt=0x11ca0
[2025-07-15 07:11:11.554] [debug] Wrote virtual memory chunk: virt=0x11ca0, size=0x1
[2025-07-15 07:11:11.554] [debug] Writing virtual memory chunk: virt=0x10ca0, size=0x10, process=0
[2025-07-15 07:11:11.554] [info] Translating virtual to physical: virt=0x10ca0, process=0
[2025-07-15 07:11:11.555] [debug] Marked page dirty: virt=0x10ca0
[2025-07-15 07:11:11.555] [debug] Wrote virtual memory chunk: virt=0x10ca0, size=0x10
[2025-07-15 07:11:11.555] [debug] Writing virtual memory chunk: virt=0x11cb0, size=0x1, process=0
[2025-07-15 07:11:11.555] [info] Translating virtual to physical: virt=0x11cb0, process=0
[2025-07-15 07:11:11.555] [debug] Marked page dirty: virt=0x11cb0
[2025-07-15 07:11:11.555] [debug] Wrote virtual memory chunk: virt=0x11cb0, size=0x1
[2025-07-15 07:11:11.555] [debug] Writing virtual memory chunk: virt=0x10cb0, size=0x10, process=0
[2025-07-15 07:11:11.555] [info] Translating virtual to physical: virt=0x10cb0, process=0
[2025-07-15 07:11:11.555] [debug] Marked page dirty: virt=0x10cb0
[2025-07-15 07:11:11.555] [debug] Wrote virtual memory chunk: virt=0x10cb0, size=0x10
[2025-07-15 07:11:11.555] [debug] Writing virtual memory chunk: virt=0x11cc0, size=0x1, process=0
[2025-07-15 07:11:11.555] [info] Translating virtual to physical: virt=0x11cc0, process=0
[2025-07-15 07:11:11.555] [debug] Marked page dirty: virt=0x11cc0
[2025-07-15 07:11:11.555] [debug] Wrote virtual memory chunk: virt=0x11cc0, size=0x1
[2025-07-15 07:11:11.555] [debug] Writing virtual memory chunk: virt=0x10cc0, size=0x10, process=0
[2025-07-15 07:11:11.556] [info] Translating virtual to physical: virt=0x10cc0, process=0
[2025-07-15 07:11:11.556] [debug] Marked page dirty: virt=0x10cc0
[2025-07-15 07:11:11.556] [debug] Wrote virtual memory chunk: virt=0x10cc0, size=0x10
[2025-07-15 07:11:11.556] [debug] Writing virtual memory chunk: virt=0x11cd0, size=0x1, process=0
[2025-07-15 07:11:11.556] [info] Translating virtual to physical: virt=0x11cd0, process=0
[2025-07-15 07:11:11.556] [debug] Marked page dirty: virt=0x11cd0
[2025-07-15 07:11:11.556] [debug] Wrote virtual memory chunk: virt=0x11cd0, size=0x1
[2025-07-15 07:11:11.556] [debug] Writing virtual memory chunk: virt=0x10cd0, size=0x10, process=0
[2025-07-15 07:11:11.556] [info] Translating virtual to physical: virt=0x10cd0, process=0
[2025-07-15 07:11:11.556] [debug] Marked page dirty: virt=0x10cd0
[2025-07-15 07:11:11.556] [debug] Wrote virtual memory chunk: virt=0x10cd0, size=0x10
[2025-07-15 07:11:11.556] [debug] Writing virtual memory chunk: virt=0x11ce0, size=0x1, process=0
[2025-07-15 07:11:11.556] [info] Translating virtual to physical: virt=0x11ce0, process=0
[2025-07-15 07:11:11.557] [debug] Marked page dirty: virt=0x11ce0
[2025-07-15 07:11:11.558] [debug] Wrote virtual memory chunk: virt=0x11ce0, size=0x1
[2025-07-15 07:11:11.558] [debug] Writing virtual memory chunk: virt=0x10ce0, size=0x10, process=0
[2025-07-15 07:11:11.558] [info] Translating virtual to physical: virt=0x10ce0, process=0
[2025-07-15 07:11:11.558] [debug] Marked page dirty: virt=0x10ce0
[2025-07-15 07:11:11.559] [debug] Wrote virtual memory chunk: virt=0x10ce0, size=0x10
[2025-07-15 07:11:11.559] [debug] Writing virtual memory chunk: virt=0x11cf0, size=0x1, process=0
[2025-07-15 07:11:11.559] [info] Translating virtual to physical: virt=0x11cf0, process=0
[2025-07-15 07:11:11.559] [debug] Marked page dirty: virt=0x11cf0
[2025-07-15 07:11:11.559] [debug] Wrote virtual memory chunk: virt=0x11cf0, size=0x1
[2025-07-15 07:11:11.559] [debug] Writing virtual memory chunk: virt=0x10cf0, size=0x10, process=0
[2025-07-15 07:11:11.559] [info] Translating virtual to physical: virt=0x10cf0, process=0
[2025-07-15 07:11:11.559] [debug] Marked page dirty: virt=0x10cf0
[2025-07-15 07:11:11.559] [debug] Wrote virtual memory chunk: virt=0x10cf0, size=0x10
[2025-07-15 07:11:11.559] [debug] Writing virtual memory chunk: virt=0x11d00, size=0x1, process=0
[2025-07-15 07:11:11.559] [info] Translating virtual to physical: virt=0x11d00, process=0
[2025-07-15 07:11:11.559] [debug] Marked page dirty: virt=0x11d00
[2025-07-15 07:11:11.559] [debug] Wrote virtual memory chunk: virt=0x11d00, size=0x1
[2025-07-15 07:11:11.559] [debug] Writing virtual memory chunk: virt=0x10d00, size=0x10, process=0
[2025-07-15 07:11:11.559] [info] Translating virtual to physical: virt=0x10d00, process=0
[2025-07-15 07:11:11.559] [debug] Marked page dirty: virt=0x10d00
[2025-07-15 07:11:11.559] [debug] Wrote virtual memory chunk: virt=0x10d00, size=0x10
[2025-07-15 07:11:11.560] [debug] Writing virtual memory chunk: virt=0x11d10, size=0x1, process=0
[2025-07-15 07:11:11.560] [info] Translating virtual to physical: virt=0x11d10, process=0
[2025-07-15 07:11:11.560] [debug] Marked page dirty: virt=0x11d10
[2025-07-15 07:11:11.560] [debug] Wrote virtual memory chunk: virt=0x11d10, size=0x1
[2025-07-15 07:11:11.560] [debug] Writing virtual memory chunk: virt=0x10d10, size=0x10, process=0
[2025-07-15 07:11:11.560] [info] Translating virtual to physical: virt=0x10d10, process=0
[2025-07-15 07:11:11.560] [debug] Marked page dirty: virt=0x10d10
[2025-07-15 07:11:11.560] [debug] Wrote virtual memory chunk: virt=0x10d10, size=0x10
[2025-07-15 07:11:11.560] [debug] Writing virtual memory chunk: virt=0x11d20, size=0x1, process=0
[2025-07-15 07:11:11.560] [info] Translating virtual to physical: virt=0x11d20, process=0
[2025-07-15 07:11:11.560] [debug] Marked page dirty: virt=0x11d20
[2025-07-15 07:11:11.560] [debug] Wrote virtual memory chunk: virt=0x11d20, size=0x1
[2025-07-15 07:11:11.560] [debug] Writing virtual memory chunk: virt=0x10d20, size=0x10, process=0
[2025-07-15 07:11:11.560] [info] Translating virtual to physical: virt=0x10d20, process=0
[2025-07-15 07:11:11.560] [debug] Marked page dirty: virt=0x10d20
[2025-07-15 07:11:11.560] [debug] Wrote virtual memory chunk: virt=0x10d20, size=0x10
[2025-07-15 07:11:11.561] [debug] Writing virtual memory chunk: virt=0x11d30, size=0x1, process=0
[2025-07-15 07:11:11.561] [info] Translating virtual to physical: virt=0x11d30, process=0
[2025-07-15 07:11:11.561] [debug] Marked page dirty: virt=0x11d30
[2025-07-15 07:11:11.561] [debug] Wrote virtual memory chunk: virt=0x11d30, size=0x1
[2025-07-15 07:11:11.561] [debug] Writing virtual memory chunk: virt=0x10d30, size=0x10, process=0
[2025-07-15 07:11:11.561] [info] Translating virtual to physical: virt=0x10d30, process=0
[2025-07-15 07:11:11.561] [debug] Marked page dirty: virt=0x10d30
[2025-07-15 07:11:11.561] [debug] Wrote virtual memory chunk: virt=0x10d30, size=0x10
[2025-07-15 07:11:11.561] [debug] Writing virtual memory chunk: virt=0x11d40, size=0x1, process=0
[2025-07-15 07:11:11.561] [info] Translating virtual to physical: virt=0x11d40, process=0
[2025-07-15 07:11:11.561] [debug] Marked page dirty: virt=0x11d40
[2025-07-15 07:11:11.561] [debug] Wrote virtual memory chunk: virt=0x11d40, size=0x1
[2025-07-15 07:11:11.561] [debug] Writing virtual memory chunk: virt=0x10d40, size=0x10, process=0
[2025-07-15 07:11:11.561] [info] Translating virtual to physical: virt=0x10d40, process=0
[2025-07-15 07:11:11.561] [debug] Marked page dirty: virt=0x10d40
[2025-07-15 07:11:11.561] [debug] Wrote virtual memory chunk: virt=0x10d40, size=0x10
[2025-07-15 07:11:11.562] [debug] Writing virtual memory chunk: virt=0x11d50, size=0x1, process=0
[2025-07-15 07:11:11.562] [info] Translating virtual to physical: virt=0x11d50, process=0
[2025-07-15 07:11:11.562] [debug] Marked page dirty: virt=0x11d50
[2025-07-15 07:11:11.562] [debug] Wrote virtual memory chunk: virt=0x11d50, size=0x1
[2025-07-15 07:11:11.562] [debug] Writing virtual memory chunk: virt=0x10d50, size=0x10, process=0
[2025-07-15 07:11:11.562] [info] Translating virtual to physical: virt=0x10d50, process=0
[2025-07-15 07:11:11.562] [debug] Marked page dirty: virt=0x10d50
[2025-07-15 07:11:11.562] [debug] Wrote virtual memory chunk: virt=0x10d50, size=0x10
[2025-07-15 07:11:11.562] [debug] Writing virtual memory chunk: virt=0x11d60, size=0x1, process=0
[2025-07-15 07:11:11.562] [info] Translating virtual to physical: virt=0x11d60, process=0
[2025-07-15 07:11:11.562] [debug] Marked page dirty: virt=0x11d60
[2025-07-15 07:11:11.562] [debug] Wrote virtual memory chunk: virt=0x11d60, size=0x1
[2025-07-15 07:11:11.562] [debug] Writing virtual memory chunk: virt=0x10d60, size=0x10, process=0
[2025-07-15 07:11:11.563] [info] Translating virtual to physical: virt=0x10d60, process=0
[2025-07-15 07:11:11.563] [debug] Marked page dirty: virt=0x10d60
[2025-07-15 07:11:11.563] [debug] Wrote virtual memory chunk: virt=0x10d60, size=0x10
[2025-07-15 07:11:11.563] [debug] Writing virtual memory chunk: virt=0x11d70, size=0x1, process=0
[2025-07-15 07:11:11.563] [info] Translating virtual to physical: virt=0x11d70, process=0
[2025-07-15 07:11:11.563] [debug] Marked page dirty: virt=0x11d70
[2025-07-15 07:11:11.563] [debug] Wrote virtual memory chunk: virt=0x11d70, size=0x1
[2025-07-15 07:11:11.563] [debug] Writing virtual memory chunk: virt=0x10d70, size=0x10, process=0
[2025-07-15 07:11:11.563] [info] Translating virtual to physical: virt=0x10d70, process=0
[2025-07-15 07:11:11.563] [debug] Marked page dirty: virt=0x10d70
[2025-07-15 07:11:11.563] [debug] Wrote virtual memory chunk: virt=0x10d70, size=0x10
[2025-07-15 07:11:11.563] [debug] Writing virtual memory chunk: virt=0x11d80, size=0x1, process=0
[2025-07-15 07:11:11.563] [info] Translating virtual to physical: virt=0x11d80, process=0
[2025-07-15 07:11:11.563] [debug] Marked page dirty: virt=0x11d80
[2025-07-15 07:11:11.563] [debug] Wrote virtual memory chunk: virt=0x11d80, size=0x1
[2025-07-15 07:11:11.564] [debug] Writing virtual memory chunk: virt=0x10d80, size=0x10, process=0
[2025-07-15 07:11:11.564] [info] Translating virtual to physical: virt=0x10d80, process=0
[2025-07-15 07:11:11.564] [debug] Marked page dirty: virt=0x10d80
[2025-07-15 07:11:11.564] [debug] Wrote virtual memory chunk: virt=0x10d80, size=0x10
[2025-07-15 07:11:11.564] [debug] Writing virtual memory chunk: virt=0x11d90, size=0x1, process=0
[2025-07-15 07:11:11.564] [info] Translating virtual to physical: virt=0x11d90, process=0
[2025-07-15 07:11:11.564] [debug] Marked page dirty: virt=0x11d90
[2025-07-15 07:11:11.564] [debug] Wrote virtual memory chunk: virt=0x11d90, size=0x1
[2025-07-15 07:11:11.564] [debug] Writing virtual memory chunk: virt=0x10d90, size=0x10, process=0
[2025-07-15 07:11:11.564] [info] Translating virtual to physical: virt=0x10d90, process=0
[2025-07-15 07:11:11.564] [debug] Marked page dirty: virt=0x10d90
[2025-07-15 07:11:11.564] [debug] Wrote virtual memory chunk: virt=0x10d90, size=0x10
[2025-07-15 07:11:11.564] [debug] Writing virtual memory chunk: virt=0x11da0, size=0x1, process=0
[2025-07-15 07:11:11.564] [info] Translating virtual to physical: virt=0x11da0, process=0
[2025-07-15 07:11:11.564] [debug] Marked page dirty: virt=0x11da0
[2025-07-15 07:11:11.564] [debug] Wrote virtual memory chunk: virt=0x11da0, size=0x1
[2025-07-15 07:11:11.564] [debug] Writing virtual memory chunk: virt=0x10da0, size=0x10, process=0
[2025-07-15 07:11:11.565] [info] Translating virtual to physical: virt=0x10da0, process=0
[2025-07-15 07:11:11.565] [debug] Marked page dirty: virt=0x10da0
[2025-07-15 07:11:11.565] [debug] Wrote virtual memory chunk: virt=0x10da0, size=0x10
[2025-07-15 07:11:11.565] [debug] Writing virtual memory chunk: virt=0x11db0, size=0x1, process=0
[2025-07-15 07:11:11.565] [info] Translating virtual to physical: virt=0x11db0, process=0
[2025-07-15 07:11:11.565] [debug] Marked page dirty: virt=0x11db0
[2025-07-15 07:11:11.565] [debug] Wrote virtual memory chunk: virt=0x11db0, size=0x1
[2025-07-15 07:11:11.565] [debug] Writing virtual memory chunk: virt=0x10db0, size=0x10, process=0
[2025-07-15 07:11:11.565] [info] Translating virtual to physical: virt=0x10db0, process=0
[2025-07-15 07:11:11.565] [debug] Marked page dirty: virt=0x10db0
[2025-07-15 07:11:11.565] [debug] Wrote virtual memory chunk: virt=0x10db0, size=0x10
[2025-07-15 07:11:11.565] [debug] Writing virtual memory chunk: virt=0x11dc0, size=0x1, process=0
[2025-07-15 07:11:11.565] [info] Translating virtual to physical: virt=0x11dc0, process=0
[2025-07-15 07:11:11.565] [debug] Marked page dirty: virt=0x11dc0
[2025-07-15 07:11:11.565] [debug] Wrote virtual memory chunk: virt=0x11dc0, size=0x1
[2025-07-15 07:11:11.566] [debug] Writing virtual memory chunk: virt=0x10dc0, size=0x10, process=0
[2025-07-15 07:11:11.566] [info] Translating virtual to physical: virt=0x10dc0, process=0
[2025-07-15 07:11:11.566] [debug] Marked page dirty: virt=0x10dc0
[2025-07-15 07:11:11.566] [debug] Wrote virtual memory chunk: virt=0x10dc0, size=0x10
[2025-07-15 07:11:11.566] [debug] Writing virtual memory chunk: virt=0x11dd0, size=0x1, process=0
[2025-07-15 07:11:11.566] [info] Translating virtual to physical: virt=0x11dd0, process=0
[2025-07-15 07:11:11.566] [debug] Marked page dirty: virt=0x11dd0
[2025-07-15 07:11:11.566] [debug] Wrote virtual memory chunk: virt=0x11dd0, size=0x1
[2025-07-15 07:11:11.566] [debug] Writing virtual memory chunk: virt=0x10dd0, size=0x10, process=0
[2025-07-15 07:11:11.566] [info] Translating virtual to physical: virt=0x10dd0, process=0
[2025-07-15 07:11:11.566] [debug] Marked page dirty: virt=0x10dd0
[2025-07-15 07:11:11.566] [debug] Wrote virtual memory chunk: virt=0x10dd0, size=0x10
[2025-07-15 07:11:11.566] [debug] Writing virtual memory chunk: virt=0x11de0, size=0x1, process=0
[2025-07-15 07:11:11.566] [info] Translating virtual to physical: virt=0x11de0, process=0
[2025-07-15 07:11:11.567] [debug] Marked page dirty: virt=0x11de0
[2025-07-15 07:11:11.567] [debug] Wrote virtual memory chunk: virt=0x11de0, size=0x1
[2025-07-15 07:11:11.567] [debug] Writing virtual memory chunk: virt=0x10de0, size=0x10, process=0
[2025-07-15 07:11:11.567] [info] Translating virtual to physical: virt=0x10de0, process=0
[2025-07-15 07:11:11.567] [debug] Marked page dirty: virt=0x10de0
[2025-07-15 07:11:11.567] [debug] Wrote virtual memory chunk: virt=0x10de0, size=0x10
[2025-07-15 07:11:11.567] [debug] Writing virtual memory chunk: virt=0x11df0, size=0x1, process=0
[2025-07-15 07:11:11.567] [info] Translating virtual to physical: virt=0x11df0, process=0
[2025-07-15 07:11:11.567] [debug] Marked page dirty: virt=0x11df0
[2025-07-15 07:11:11.567] [debug] Wrote virtual memory chunk: virt=0x11df0, size=0x1
[2025-07-15 07:11:11.567] [debug] Writing virtual memory chunk: virt=0x10df0, size=0x10, process=0
[2025-07-15 07:11:11.567] [info] Translating virtual to physical: virt=0x10df0, process=0
[2025-07-15 07:11:11.567] [debug] Marked page dirty: virt=0x10df0
[2025-07-15 07:11:11.567] [debug] Wrote virtual memory chunk: virt=0x10df0, size=0x10
[2025-07-15 07:11:11.567] [debug] Writing virtual memory chunk: virt=0x11e00, size=0x1, process=0
[2025-07-15 07:11:11.568] [info] Translating virtual to physical: virt=0x11e00, process=0
[2025-07-15 07:11:11.568] [debug] Marked page dirty: virt=0x11e00
[2025-07-15 07:11:11.568] [debug] Wrote virtual memory chunk: virt=0x11e00, size=0x1
[2025-07-15 07:11:11.568] [debug] Writing virtual memory chunk: virt=0x10e00, size=0x10, process=0
[2025-07-15 07:11:11.568] [info] Translating virtual to physical: virt=0x10e00, process=0
[2025-07-15 07:11:11.568] [debug] Marked page dirty: virt=0x10e00
[2025-07-15 07:11:11.568] [debug] Wrote virtual memory chunk: virt=0x10e00, size=0x10
[2025-07-15 07:11:11.568] [debug] Writing virtual memory chunk: virt=0x11e10, size=0x1, process=0
[2025-07-15 07:11:11.568] [info] Translating virtual to physical: virt=0x11e10, process=0
[2025-07-15 07:11:11.568] [debug] Marked page dirty: virt=0x11e10
[2025-07-15 07:11:11.568] [debug] Wrote virtual memory chunk: virt=0x11e10, size=0x1
[2025-07-15 07:11:11.568] [debug] Writing virtual memory chunk: virt=0x10e10, size=0x10, process=0
[2025-07-15 07:11:11.568] [info] Translating virtual to physical: virt=0x10e10, process=0
[2025-07-15 07:11:11.568] [debug] Marked page dirty: virt=0x10e10
[2025-07-15 07:11:11.568] [debug] Wrote virtual memory chunk: virt=0x10e10, size=0x10
[2025-07-15 07:11:11.568] [debug] Writing virtual memory chunk: virt=0x11e20, size=0x1, process=0
[2025-07-15 07:11:11.569] [info] Translating virtual to physical: virt=0x11e20, process=0
[2025-07-15 07:11:11.569] [debug] Marked page dirty: virt=0x11e20
[2025-07-15 07:11:11.569] [debug] Wrote virtual memory chunk: virt=0x11e20, size=0x1
[2025-07-15 07:11:11.569] [debug] Writing virtual memory chunk: virt=0x10e20, size=0x10, process=0
[2025-07-15 07:11:11.569] [info] Translating virtual to physical: virt=0x10e20, process=0
[2025-07-15 07:11:11.569] [debug] Marked page dirty: virt=0x10e20
[2025-07-15 07:11:11.569] [debug] Wrote virtual memory chunk: virt=0x10e20, size=0x10
[2025-07-15 07:11:11.569] [debug] Writing virtual memory chunk: virt=0x11e30, size=0x1, process=0
[2025-07-15 07:11:11.569] [info] Translating virtual to physical: virt=0x11e30, process=0
[2025-07-15 07:11:11.569] [debug] Marked page dirty: virt=0x11e30
[2025-07-15 07:11:11.569] [debug] Wrote virtual memory chunk: virt=0x11e30, size=0x1
[2025-07-15 07:11:11.569] [debug] Writing virtual memory chunk: virt=0x10e30, size=0x10, process=0
[2025-07-15 07:11:11.569] [info] Translating virtual to physical: virt=0x10e30, process=0
[2025-07-15 07:11:11.569] [debug] Marked page dirty: virt=0x10e30
[2025-07-15 07:11:11.569] [debug] Wrote virtual memory chunk: virt=0x10e30, size=0x10
[2025-07-15 07:11:11.570] [debug] Writing virtual memory chunk: virt=0x11e40, size=0x1, process=0
[2025-07-15 07:11:11.570] [info] Translating virtual to physical: virt=0x11e40, process=0
[2025-07-15 07:11:11.570] [debug] Marked page dirty: virt=0x11e40
[2025-07-15 07:11:11.570] [debug] Wrote virtual memory chunk: virt=0x11e40, size=0x1
[2025-07-15 07:11:11.570] [debug] Writing virtual memory chunk: virt=0x10e40, size=0x10, process=0
[2025-07-15 07:11:11.570] [info] Translating virtual to physical: virt=0x10e40, process=0
[2025-07-15 07:11:11.570] [debug] Marked page dirty: virt=0x10e40
[2025-07-15 07:11:11.570] [debug] Wrote virtual memory chunk: virt=0x10e40, size=0x10
[2025-07-15 07:11:11.570] [debug] Writing virtual memory chunk: virt=0x11e50, size=0x1, process=0
[2025-07-15 07:11:11.570] [info] Translating virtual to physical: virt=0x11e50, process=0
[2025-07-15 07:11:11.570] [debug] Marked page dirty: virt=0x11e50
[2025-07-15 07:11:11.570] [debug] Wrote virtual memory chunk: virt=0x11e50, size=0x1
[2025-07-15 07:11:11.570] [debug] Writing virtual memory chunk: virt=0x10e50, size=0x10, process=0
[2025-07-15 07:11:11.570] [info] Translating virtual to physical: virt=0x10e50, process=0
[2025-07-15 07:11:11.570] [debug] Marked page dirty: virt=0x10e50
[2025-07-15 07:11:11.571] [debug] Wrote virtual memory chunk: virt=0x10e50, size=0x10
[2025-07-15 07:11:11.571] [debug] Writing virtual memory chunk: virt=0x11e60, size=0x1, process=0
[2025-07-15 07:11:11.571] [info] Translating virtual to physical: virt=0x11e60, process=0
[2025-07-15 07:11:11.571] [debug] Marked page dirty: virt=0x11e60
[2025-07-15 07:11:11.571] [debug] Wrote virtual memory chunk: virt=0x11e60, size=0x1
[2025-07-15 07:11:11.571] [debug] Writing virtual memory chunk: virt=0x10e60, size=0x10, process=0
[2025-07-15 07:11:11.571] [info] Translating virtual to physical: virt=0x10e60, process=0
[2025-07-15 07:11:11.571] [debug] Marked page dirty: virt=0x10e60
[2025-07-15 07:11:11.571] [debug] Wrote virtual memory chunk: virt=0x10e60, size=0x10
[2025-07-15 07:11:11.571] [debug] Writing virtual memory chunk: virt=0x11e70, size=0x1, process=0
[2025-07-15 07:11:11.571] [info] Translating virtual to physical: virt=0x11e70, process=0
[2025-07-15 07:11:11.571] [debug] Marked page dirty: virt=0x11e70
[2025-07-15 07:11:11.571] [debug] Wrote virtual memory chunk: virt=0x11e70, size=0x1
[2025-07-15 07:11:11.571] [debug] Writing virtual memory chunk: virt=0x10e70, size=0x10, process=0
[2025-07-15 07:11:11.571] [info] Translating virtual to physical: virt=0x10e70, process=0
[2025-07-15 07:11:11.572] [debug] Marked page dirty: virt=0x10e70
[2025-07-15 07:11:11.572] [debug] Wrote virtual memory chunk: virt=0x10e70, size=0x10
[2025-07-15 07:11:11.572] [debug] Writing virtual memory chunk: virt=0x11e80, size=0x1, process=0
[2025-07-15 07:11:11.572] [info] Translating virtual to physical: virt=0x11e80, process=0
[2025-07-15 07:11:11.572] [debug] Marked page dirty: virt=0x11e80
[2025-07-15 07:11:11.572] [debug] Wrote virtual memory chunk: virt=0x11e80, size=0x1
[2025-07-15 07:11:11.572] [debug] Writing virtual memory chunk: virt=0x10e80, size=0x10, process=0
[2025-07-15 07:11:11.574] [info] Translating virtual to physical: virt=0x10e80, process=0
[2025-07-15 07:11:11.574] [debug] Marked page dirty: virt=0x10e80
[2025-07-15 07:11:11.574] [debug] Wrote virtual memory chunk: virt=0x10e80, size=0x10
[2025-07-15 07:11:11.574] [debug] Writing virtual memory chunk: virt=0x11e90, size=0x1, process=0
[2025-07-15 07:11:11.574] [info] Translating virtual to physical: virt=0x11e90, process=0
[2025-07-15 07:11:11.574] [debug] Marked page dirty: virt=0x11e90
[2025-07-15 07:11:11.574] [debug] Wrote virtual memory chunk: virt=0x11e90, size=0x1
[2025-07-15 07:11:11.575] [debug] Writing virtual memory chunk: virt=0x10e90, size=0x10, process=0
[2025-07-15 07:11:11.575] [info] Translating virtual to physical: virt=0x10e90, process=0
[2025-07-15 07:11:11.575] [debug] Marked page dirty: virt=0x10e90
[2025-07-15 07:11:11.575] [debug] Wrote virtual memory chunk: virt=0x10e90, size=0x10
[2025-07-15 07:11:11.575] [debug] Writing virtual memory chunk: virt=0x11ea0, size=0x1, process=0
[2025-07-15 07:11:11.575] [info] Translating virtual to physical: virt=0x11ea0, process=0
[2025-07-15 07:11:11.575] [debug] Marked page dirty: virt=0x11ea0
[2025-07-15 07:11:11.575] [debug] Wrote virtual memory chunk: virt=0x11ea0, size=0x1
[2025-07-15 07:11:11.575] [debug] Writing virtual memory chunk: virt=0x10ea0, size=0x10, process=0
[2025-07-15 07:11:11.575] [info] Translating virtual to physical: virt=0x10ea0, process=0
[2025-07-15 07:11:11.575] [debug] Marked page dirty: virt=0x10ea0
[2025-07-15 07:11:11.575] [debug] Wrote virtual memory chunk: virt=0x10ea0, size=0x10
[2025-07-15 07:11:11.575] [debug] Writing virtual memory chunk: virt=0x11eb0, size=0x1, process=0
[2025-07-15 07:11:11.575] [info] Translating virtual to physical: virt=0x11eb0, process=0
[2025-07-15 07:11:11.575] [debug] Marked page dirty: virt=0x11eb0
[2025-07-15 07:11:11.575] [debug] Wrote virtual memory chunk: virt=0x11eb0, size=0x1
[2025-07-15 07:11:11.575] [debug] Writing virtual memory chunk: virt=0x10eb0, size=0x10, process=0
[2025-07-15 07:11:11.575] [info] Translating virtual to physical: virt=0x10eb0, process=0
[2025-07-15 07:11:11.576] [debug] Marked page dirty: virt=0x10eb0
[2025-07-15 07:11:11.576] [debug] Wrote virtual memory chunk: virt=0x10eb0, size=0x10
[2025-07-15 07:11:11.576] [debug] Writing virtual memory chunk: virt=0x11ec0, size=0x1, process=0
[2025-07-15 07:11:11.576] [info] Translating virtual to physical: virt=0x11ec0, process=0
[2025-07-15 07:11:11.576] [debug] Marked page dirty: virt=0x11ec0
[2025-07-15 07:11:11.576] [debug] Wrote virtual memory chunk: virt=0x11ec0, size=0x1
[2025-07-15 07:11:11.576] [debug] Writing virtual memory chunk: virt=0x10ec0, size=0x10, process=0
[2025-07-15 07:11:11.576] [info] Translating virtual to physical: virt=0x10ec0, process=0
[2025-07-15 07:11:11.576] [debug] Marked page dirty: virt=0x10ec0
[2025-07-15 07:11:11.576] [debug] Wrote virtual memory chunk: virt=0x10ec0, size=0x10
[2025-07-15 07:11:11.576] [debug] Writing virtual memory chunk: virt=0x11ed0, size=0x1, process=0
[2025-07-15 07:11:11.576] [info] Translating virtual to physical: virt=0x11ed0, process=0
[2025-07-15 07:11:11.576] [debug] Marked page dirty: virt=0x11ed0
[2025-07-15 07:11:11.577] [debug] Wrote virtual memory chunk: virt=0x11ed0, size=0x1
[2025-07-15 07:11:11.577] [debug] Writing virtual memory chunk: virt=0x10ed0, size=0x10, process=0
[2025-07-15 07:11:11.577] [info] Translating virtual to physical: virt=0x10ed0, process=0
[2025-07-15 07:11:11.577] [debug] Marked page dirty: virt=0x10ed0
[2025-07-15 07:11:11.577] [debug] Wrote virtual memory chunk: virt=0x10ed0, size=0x10
[2025-07-15 07:11:11.577] [debug] Writing virtual memory chunk: virt=0x11ee0, size=0x1, process=0
[2025-07-15 07:11:11.577] [info] Translating virtual to physical: virt=0x11ee0, process=0
[2025-07-15 07:11:11.577] [debug] Marked page dirty: virt=0x11ee0
[2025-07-15 07:11:11.577] [debug] Wrote virtual memory chunk: virt=0x11ee0, size=0x1
[2025-07-15 07:11:11.577] [debug] Writing virtual memory chunk: virt=0x10ee0, size=0x10, process=0
[2025-07-15 07:11:11.577] [info] Translating virtual to physical: virt=0x10ee0, process=0
[2025-07-15 07:11:11.577] [debug] Marked page dirty: virt=0x10ee0
[2025-07-15 07:11:11.577] [debug] Wrote virtual memory chunk: virt=0x10ee0, size=0x10
[2025-07-15 07:11:11.577] [debug] Writing virtual memory chunk: virt=0x11ef0, size=0x1, process=0
[2025-07-15 07:11:11.577] [info] Translating virtual to physical: virt=0x11ef0, process=0
[2025-07-15 07:11:11.578] [debug] Marked page dirty: virt=0x11ef0
[2025-07-15 07:11:11.578] [debug] Wrote virtual memory chunk: virt=0x11ef0, size=0x1
[2025-07-15 07:11:11.578] [debug] Writing virtual memory chunk: virt=0x10ef0, size=0x10, process=0
[2025-07-15 07:11:11.578] [info] Translating virtual to physical: virt=0x10ef0, process=0
[2025-07-15 07:11:11.578] [debug] Marked page dirty: virt=0x10ef0
[2025-07-15 07:11:11.578] [debug] Wrote virtual memory chunk: virt=0x10ef0, size=0x10
[2025-07-15 07:11:11.578] [debug] Writing virtual memory chunk: virt=0x11f00, size=0x1, process=0
[2025-07-15 07:11:11.578] [info] Translating virtual to physical: virt=0x11f00, process=0
[2025-07-15 07:11:11.578] [debug] Marked page dirty: virt=0x11f00
[2025-07-15 07:11:11.578] [debug] Wrote virtual memory chunk: virt=0x11f00, size=0x1
[2025-07-15 07:11:11.578] [debug] Writing virtual memory chunk: virt=0x10f00, size=0x10, process=0
[2025-07-15 07:11:11.578] [info] Translating virtual to physical: virt=0x10f00, process=0
[2025-07-15 07:11:11.578] [debug] Marked page dirty: virt=0x10f00
[2025-07-15 07:11:11.578] [debug] Wrote virtual memory chunk: virt=0x10f00, size=0x10
[2025-07-15 07:11:11.578] [debug] Writing virtual memory chunk: virt=0x11f10, size=0x1, process=0
[2025-07-15 07:11:11.578] [info] Translating virtual to physical: virt=0x11f10, process=0
[2025-07-15 07:11:11.579] [debug] Marked page dirty: virt=0x11f10
[2025-07-15 07:11:11.579] [debug] Wrote virtual memory chunk: virt=0x11f10, size=0x1
[2025-07-15 07:11:11.579] [debug] Writing virtual memory chunk: virt=0x10f10, size=0x10, process=0
[2025-07-15 07:11:11.579] [info] Translating virtual to physical: virt=0x10f10, process=0
[2025-07-15 07:11:11.579] [debug] Marked page dirty: virt=0x10f10
[2025-07-15 07:11:11.579] [debug] Wrote virtual memory chunk: virt=0x10f10, size=0x10
[2025-07-15 07:11:11.579] [debug] Writing virtual memory chunk: virt=0x11f20, size=0x1, process=0
[2025-07-15 07:11:11.579] [info] Translating virtual to physical: virt=0x11f20, process=0
[2025-07-15 07:11:11.579] [debug] Marked page dirty: virt=0x11f20
[2025-07-15 07:11:11.579] [debug] Wrote virtual memory chunk: virt=0x11f20, size=0x1
[2025-07-15 07:11:11.579] [debug] Writing virtual memory chunk: virt=0x10f20, size=0x10, process=0
[2025-07-15 07:11:11.579] [info] Translating virtual to physical: virt=0x10f20, process=0
[2025-07-15 07:11:11.579] [debug] Marked page dirty: virt=0x10f20
[2025-07-15 07:11:11.579] [debug] Wrote virtual memory chunk: virt=0x10f20, size=0x10
[2025-07-15 07:11:11.580] [debug] Writing virtual memory chunk: virt=0x11f30, size=0x1, process=0
[2025-07-15 07:11:11.580] [info] Translating virtual to physical: virt=0x11f30, process=0
[2025-07-15 07:11:11.580] [debug] Marked page dirty: virt=0x11f30
[2025-07-15 07:11:11.580] [debug] Wrote virtual memory chunk: virt=0x11f30, size=0x1
[2025-07-15 07:11:11.580] [debug] Writing virtual memory chunk: virt=0x10f30, size=0x10, process=0
[2025-07-15 07:11:11.580] [info] Translating virtual to physical: virt=0x10f30, process=0
[2025-07-15 07:11:11.580] [debug] Marked page dirty: virt=0x10f30
[2025-07-15 07:11:11.580] [debug] Wrote virtual memory chunk: virt=0x10f30, size=0x10
[2025-07-15 07:11:11.580] [debug] Writing virtual memory chunk: virt=0x11f40, size=0x1, process=0
[2025-07-15 07:11:11.580] [info] Translating virtual to physical: virt=0x11f40, process=0
[2025-07-15 07:11:11.580] [debug] Marked page dirty: virt=0x11f40
[2025-07-15 07:11:11.580] [debug] Wrote virtual memory chunk: virt=0x11f40, size=0x1
[2025-07-15 07:11:11.580] [debug] Writing virtual memory chunk: virt=0x10f40, size=0x10, process=0
[2025-07-15 07:11:11.580] [info] Translating virtual to physical: virt=0x10f40, process=0
[2025-07-15 07:11:11.580] [debug] Marked page dirty: virt=0x10f40
[2025-07-15 07:11:11.580] [debug] Wrote virtual memory chunk: virt=0x10f40, size=0x10
[2025-07-15 07:11:11.581] [debug] Writing virtual memory chunk: virt=0x11f50, size=0x1, process=0
[2025-07-15 07:11:11.581] [info] Translating virtual to physical: virt=0x11f50, process=0
[2025-07-15 07:11:11.581] [debug] Marked page dirty: virt=0x11f50
[2025-07-15 07:11:11.581] [debug] Wrote virtual memory chunk: virt=0x11f50, size=0x1
[2025-07-15 07:11:11.581] [debug] Writing virtual memory chunk: virt=0x10f50, size=0x10, process=0
[2025-07-15 07:11:11.581] [info] Translating virtual to physical: virt=0x10f50, process=0
[2025-07-15 07:11:11.581] [debug] Marked page dirty: virt=0x10f50
[2025-07-15 07:11:11.581] [debug] Wrote virtual memory chunk: virt=0x10f50, size=0x10
[2025-07-15 07:11:11.581] [debug] Writing virtual memory chunk: virt=0x11f60, size=0x1, process=0
[2025-07-15 07:11:11.581] [info] Translating virtual to physical: virt=0x11f60, process=0
[2025-07-15 07:11:11.581] [debug] Marked page dirty: virt=0x11f60
[2025-07-15 07:11:11.581] [debug] Wrote virtual memory chunk: virt=0x11f60, size=0x1
[2025-07-15 07:11:11.581] [debug] Writing virtual memory chunk: virt=0x10f60, size=0x10, process=0
[2025-07-15 07:11:11.581] [info] Translating virtual to physical: virt=0x10f60, process=0
[2025-07-15 07:11:11.581] [debug] Marked page dirty: virt=0x10f60
[2025-07-15 07:11:11.582] [debug] Wrote virtual memory chunk: virt=0x10f60, size=0x10
[2025-07-15 07:11:11.582] [debug] Writing virtual memory chunk: virt=0x11f70, size=0x1, process=0
[2025-07-15 07:11:11.582] [info] Translating virtual to physical: virt=0x11f70, process=0
[2025-07-15 07:11:11.582] [debug] Marked page dirty: virt=0x11f70
[2025-07-15 07:11:11.582] [debug] Wrote virtual memory chunk: virt=0x11f70, size=0x1
[2025-07-15 07:11:11.582] [debug] Writing virtual memory chunk: virt=0x10f70, size=0x10, process=0
[2025-07-15 07:11:11.582] [info] Translating virtual to physical: virt=0x10f70, process=0
[2025-07-15 07:11:11.582] [debug] Marked page dirty: virt=0x10f70
[2025-07-15 07:11:11.582] [debug] Wrote virtual memory chunk: virt=0x10f70, size=0x10
[2025-07-15 07:11:11.582] [debug] Writing virtual memory chunk: virt=0x11f80, size=0x1, process=0
[2025-07-15 07:11:11.583] [info] Translating virtual to physical: virt=0x11f80, process=0
[2025-07-15 07:11:11.583] [debug] Marked page dirty: virt=0x11f80
[2025-07-15 07:11:11.583] [debug] Wrote virtual memory chunk: virt=0x11f80, size=0x1
[2025-07-15 07:11:11.583] [debug] Writing virtual memory chunk: virt=0x10f80, size=0x10, process=0
[2025-07-15 07:11:11.583] [info] Translating virtual to physical: virt=0x10f80, process=0
[2025-07-15 07:11:11.583] [debug] Marked page dirty: virt=0x10f80
[2025-07-15 07:11:11.583] [debug] Wrote virtual memory chunk: virt=0x10f80, size=0x10
[2025-07-15 07:11:11.583] [debug] Writing virtual memory chunk: virt=0x11f90, size=0x1, process=0
[2025-07-15 07:11:11.583] [info] Translating virtual to physical: virt=0x11f90, process=0
[2025-07-15 07:11:11.583] [debug] Marked page dirty: virt=0x11f90
[2025-07-15 07:11:11.583] [debug] Wrote virtual memory chunk: virt=0x11f90, size=0x1
[2025-07-15 07:11:11.583] [debug] Writing virtual memory chunk: virt=0x10f90, size=0x10, process=0
[2025-07-15 07:11:11.583] [info] Translating virtual to physical: virt=0x10f90, process=0
[2025-07-15 07:11:11.583] [debug] Marked page dirty: virt=0x10f90
[2025-07-15 07:11:11.583] [debug] Wrote virtual memory chunk: virt=0x10f90, size=0x10
[2025-07-15 07:11:11.583] [debug] Writing virtual memory chunk: virt=0x11fa0, size=0x1, process=0
[2025-07-15 07:11:11.584] [info] Translating virtual to physical: virt=0x11fa0, process=0
[2025-07-15 07:11:11.584] [debug] Marked page dirty: virt=0x11fa0
[2025-07-15 07:11:11.584] [debug] Wrote virtual memory chunk: virt=0x11fa0, size=0x1
[2025-07-15 07:11:11.584] [debug] Writing virtual memory chunk: virt=0x10fa0, size=0x10, process=0
[2025-07-15 07:11:11.584] [info] Translating virtual to physical: virt=0x10fa0, process=0
[2025-07-15 07:11:11.584] [debug] Marked page dirty: virt=0x10fa0
[2025-07-15 07:11:11.584] [debug] Wrote virtual memory chunk: virt=0x10fa0, size=0x10
[2025-07-15 07:11:11.584] [info] Processed descriptor 250
[2025-07-15 07:11:11.584] [debug] Writing virtual memory chunk: virt=0x11fb0, size=0x1, process=0
[2025-07-15 07:11:11.584] [info] Translating virtual to physical: virt=0x11fb0, process=0
[2025-07-15 07:11:11.584] [debug] Marked page dirty: virt=0x11fb0
[2025-07-15 07:11:11.584] [debug] Wrote virtual memory chunk: virt=0x11fb0, size=0x1
[2025-07-15 07:11:11.584] [debug] Writing virtual memory chunk: virt=0x10fb0, size=0x10, process=0
[2025-07-15 07:11:11.584] [info] Translating virtual to physical: virt=0x10fb0, process=0
[2025-07-15 07:11:11.584] [debug] Marked page dirty: virt=0x10fb0
[2025-07-15 07:11:11.584] [debug] Wrote virtual memory chunk: virt=0x10fb0, size=0x10
[2025-07-15 07:11:11.585] [debug] Writing virtual memory chunk: virt=0x11fc0, size=0x1, process=0
[2025-07-15 07:11:11.585] [info] Translating virtual to physical: virt=0x11fc0, process=0
[2025-07-15 07:11:11.585] [debug] Marked page dirty: virt=0x11fc0
[2025-07-15 07:11:11.585] [debug] Wrote virtual memory chunk: virt=0x11fc0, size=0x1
[2025-07-15 07:11:11.585] [debug] Writing virtual memory chunk: virt=0x10fc0, size=0x10, process=0
[2025-07-15 07:11:11.585] [info] Translating virtual to physical: virt=0x10fc0, process=0
[2025-07-15 07:11:11.585] [debug] Marked page dirty: virt=0x10fc0
[2025-07-15 07:11:11.585] [debug] Wrote virtual memory chunk: virt=0x10fc0, size=0x10
[2025-07-15 07:11:11.585] [debug] Writing virtual memory chunk: virt=0x11fd0, size=0x1, process=0
[2025-07-15 07:11:11.585] [info] Translating virtual to physical: virt=0x11fd0, process=0
[2025-07-15 07:11:11.585] [debug] Marked page dirty: virt=0x11fd0
[2025-07-15 07:11:11.585] [debug] Wrote virtual memory chunk: virt=0x11fd0, size=0x1
[2025-07-15 07:11:11.585] [debug] Writing virtual memory chunk: virt=0x10fd0, size=0x10, process=0
[2025-07-15 07:11:11.585] [info] Translating virtual to physical: virt=0x10fd0, process=0
[2025-07-15 07:11:11.585] [debug] Marked page dirty: virt=0x10fd0
[2025-07-15 07:11:11.585] [debug] Wrote virtual memory chunk: virt=0x10fd0, size=0x10
[2025-07-15 07:11:11.586] [debug] Writing virtual memory chunk: virt=0x11fe0, size=0x1, process=0
[2025-07-15 07:11:11.586] [info] Translating virtual to physical: virt=0x11fe0, process=0
[2025-07-15 07:11:11.586] [debug] Marked page dirty: virt=0x11fe0
[2025-07-15 07:11:11.586] [debug] Wrote virtual memory chunk: virt=0x11fe0, size=0x1
[2025-07-15 07:11:11.586] [debug] Writing virtual memory chunk: virt=0x10fe0, size=0x10, process=0
[2025-07-15 07:11:11.586] [info] Translating virtual to physical: virt=0x10fe0, process=0
[2025-07-15 07:11:11.586] [debug] Marked page dirty: virt=0x10fe0
[2025-07-15 07:11:11.586] [debug] Wrote virtual memory chunk: virt=0x10fe0, size=0x10
[2025-07-15 07:11:11.586] [debug] Writing virtual memory chunk: virt=0x11ff0, size=0x1, process=0
[2025-07-15 07:11:11.586] [info] Translating virtual to physical: virt=0x11ff0, process=0
[2025-07-15 07:11:11.586] [debug] Marked page dirty: virt=0x11ff0
[2025-07-15 07:11:11.586] [debug] Wrote virtual memory chunk: virt=0x11ff0, size=0x1
[2025-07-15 07:11:11.586] [debug] Writing virtual memory chunk: virt=0x10ff0, size=0x10, process=0
[2025-07-15 07:11:11.586] [info] Translating virtual to physical: virt=0x10ff0, process=0
[2025-07-15 07:11:11.586] [debug] Marked page dirty: virt=0x10ff0
[2025-07-15 07:11:11.587] [debug] Wrote virtual memory chunk: virt=0x10ff0, size=0x10
[2025-07-15 07:11:11.587] [info] IDT descriptors set
[2025-07-15 07:11:11.587] [info] Setting up IDT descriptor
[2025-07-15 07:11:11.587] [info] IDT descriptor set: base=0x10000, limit=0xfff
[2025-07-15 07:11:11.587] [info] Custom handlers registered
[2025-07-15 07:11:11.587] [info] Loading IDT
[2025-07-15 07:11:11.587] [info] Attempting to load IDT...
[2025-07-15 07:11:11.587] [warning] LoadIDT not implemented for MSVC; simulating IDT load
[2025-07-15 07:11:11.587] [info] X86_64CPU[0]: Set IDTR: base=0x10000, limit=0xfff
[2025-07-15 07:11:11.587] [info] LoadIDT completed
[2025-07-15 07:11:11.587] [info] IDT loaded successfully
[2025-07-15 07:11:11.587] [info] InterruptHandler initialization completed
[2025-07-15 07:11:11.598] [info] InterruptHandler initialized successfully in 167ms
[2025-07-15 07:11:11.598] [info] Step 8/10: Finalizing emulator state...
[2025-07-15 07:11:11.598] [info] Memory diagnostics reset
[2025-07-15 07:11:11.598] [info] CPU diagnostics reset
[2025-07-15 07:11:11.598] [info] CPUDiagnostics initialized
[2025-07-15 07:11:11.598] [info] CPU diagnostics reset
[2025-07-15 07:11:11.598] [info] JIT diagnostics reset
[2025-07-15 07:11:11.598] [info] JITDiagnostics initialized
[2025-07-15 07:11:11.599] [info] JIT diagnostics reset
[2025-07-15 07:11:11.599] [info] === PS4 Emulator Initialization Complete ===
[2025-07-15 07:11:11.599] [info] Total initialization time: 851ms (851121us)
[2025-07-15 07:11:11.606] [info] PS4 emulator initialized successfully
[2025-07-15 07:11:11.606] [info] InputManager constructed
[2025-07-15 07:11:11.606] [info] InputManager initialized
[2025-07-15 07:11:11.606] [error] Failed to list games in directory ./ps4_root/app0: directory_iterator::directory_iterator: The system cannot find the path specified.: "./ps4_root/app0"
[2025-07-15 07:11:11.613] [info] AudioDevice started
[2025-07-15 07:11:11.614] [info] Applied audio settings: enabled=true, master_volume=1, underrun_warning_interval=5s
[2025-07-15 07:11:11.614] [info] Applied CPU settings: threads=12, jit_enabled=true, simd_optimizations=true, branch_prediction=true, cache_size_mb=256
[2025-07-15 07:11:11.614] [warning] CPU settings like thread count, JIT, SIMD, branch prediction usually require emulator restart to take full effect.
[2025-07-15 07:11:11.614] [info] Applied GPU settings: backend=Vulkan, resolution_scale=4, anisotropic_filtering=true, anti_aliasing=true
[2025-07-15 07:11:11.614] [warning] Changing GPU backend usually requires emulator restart.
[2025-07-15 07:11:11.614] [info] Applied memory settings: size_gb=10, compression=true, swap_size_gb=6
[2025-07-15 07:11:11.614] [warning] Memory settings usually require emulator restart to take full effect.
[2025-07-15 07:11:11.614] [info] Applied filesystem settings: game_directory=C:/Users/<USER>/Downloads/ps4_root/, auto_mount_games=true
[2025-07-15 07:11:11.614] [info] Game directory set to: C:/Users/<USER>/Downloads/ps4_root/
[2025-07-15 07:11:11.614] [info] Applied debug settings: debug_mode=true, log_syscalls=true, log_gpu_commands=true, log_level=debug
[2025-07-15 07:11:11.615] [info] Applied input settings: controller_enabled=true, keyboard_enabled=true, mouse_enabled=true, deadzone=0.1
[2025-07-15 07:11:11.615] [info] Applied network settings: enabled=false, interface=auto, port=9302
[2025-07-15 07:11:11.615] [info] Applied compatibility settings: strict_mode=true, ignore_missing_imports=false, patch_games=false
[2025-07-15 07:11:11.615] [info] Applied advanced settings: emulation_speed=1, auto_save_states=true, auto_save_interval=300
[2025-07-15 07:11:11.615] [info] Controller input thread started.
[2025-07-15 07:11:16.030] [warning] Audio buffer underrun, count=832, queue_size=0, time_since_last=5s
[2025-07-15 07:11:21.031] [warning] Audio buffer underrun, count=1769, queue_size=0, time_since_last=5s
[2025-07-15 07:11:26.039] [warning] Audio buffer underrun, count=2708, queue_size=0, time_since_last=5s
[2025-07-15 07:11:31.048] [warning] Audio buffer underrun, count=3648, queue_size=0, time_since_last=5s
[2025-07-15 07:11:36.056] [warning] Audio buffer underrun, count=4587, queue_size=0, time_since_last=5s
[2025-07-15 07:11:41.066] [warning] Audio buffer underrun, count=5527, queue_size=0, time_since_last=5s
[2025-07-15 07:11:46.068] [warning] Audio buffer underrun, count=6464, queue_size=0, time_since_last=5s
[2025-07-15 07:11:51.077] [warning] Audio buffer underrun, count=7403, queue_size=0, time_since_last=5s
[2025-07-15 07:11:56.078] [warning] Audio buffer underrun, count=8341, queue_size=0, time_since_last=5s
[2025-07-15 07:11:56.856] [info] LoadGame: Loading ELF/BIN file: 
[2025-07-15 07:11:56.856] [info] ElfLoader initialized
[2025-07-15 07:11:56.857] [info] Loading ELF/SELF: , process ID: 1, shared: false
[2025-07-15 07:11:56.857] [debug] MapToHostPath: Starting mapping for ''
[2025-07-15 07:11:56.857] [debug] MapToHostPath: virtualPath='', mountPoint result=''
[2025-07-15 07:11:56.857] [debug] MapToHostPath: using fallback, result='./ps4_root\'
[2025-07-15 07:11:56.857] [error] Failed to open file: ./ps4_root\
[2025-07-15 07:11:56.857] [error] ELF/SELF load failed for '': File open failure
[2025-07-15 07:11:56.857] [error] LoadGame: Failed to load ELF: 
[2025-07-15 07:11:56.857] [info] ElfLoader destroyed
[2025-07-15 07:11:56.857] [error] Failed to load game: 
[2025-07-15 07:12:01.087] [warning] Audio buffer underrun, count=9280, queue_size=0, time_since_last=5s
[2025-07-15 07:12:06.090] [warning] Audio buffer underrun, count=10218, queue_size=0, time_since_last=5s
[2025-07-15 07:12:11.056] [info] LoadGame: Loading ELF/BIN file: D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin
[2025-07-15 07:12:11.056] [info] ElfLoader initialized
[2025-07-15 07:12:11.057] [info] Loading ELF/SELF: D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin, process ID: 1, shared: false
[2025-07-15 07:12:11.057] [debug] MapToHostPath: Starting mapping for 'D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin'
[2025-07-15 07:12:11.057] [debug] MapToHostPath: virtualPath='D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin', mountPoint result=''
[2025-07-15 07:12:11.057] [debug] MapToHostPath: using fallback, result='D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin'
[2025-07-15 07:12:11.058] [debug] File size: 5803753 bytes
[2025-07-15 07:12:11.073] [debug] Data appears to be a decrypted ELF file, not an encrypted SELF
[2025-07-15 07:12:11.074] [info] Detected decrypted ELF file: D:\sss\build\Debug\ps4_root\installed_packages\eboot.bin, no decryption needed
[2025-07-15 07:12:11.074] [debug] ELF header details: magic=0x7F454C46, class=0x02, data=0x01, version=0x01, machine=0x003E, type=0xFE00, entry=0x0000000000638580, phoff=0x0000000000000040, shoff=0x0000000000000000, phnum=10, shnum=0
[2025-07-15 07:12:11.074] [info] ELF type: 0xFE00 (Static)
[2025-07-15 07:12:11.074] [debug] LoadSegments: Starting segment loading for ELF type 0xFE00
[2025-07-15 07:12:11.075] [debug] LoadSegments: Reading 10 program headers from offset 0x0000000000000040
[2025-07-15 07:12:11.075] [debug] Program header 0: type=0x00000001, offset=0x0000000000004000, vaddr=0x0000000000400000, filesz=0x000000000041A8C0, memsz=0x000000000041A8C0, flags=0x00000005, align=0x0000000000004000
[2025-07-15 07:12:11.075] [debug] Program header 1: type=0x00000001, offset=0x0000000000420000, vaddr=0x000000000081C000, filesz=0x00000000000D9C90, memsz=0x00000000031EFA08, flags=0x00000006, align=0x0000000000004000
[2025-07-15 07:12:11.075] [debug] Program header 2: type=0x61000001, offset=0x0000000000420000, vaddr=0x000000000081C000, filesz=0x0000000000000040, memsz=0x0000000000000040, flags=0x00000006, align=0x0000000000000008
[2025-07-15 07:12:11.075] [debug] Program header 3: type=0x00000002, offset=0x0000000000587FD0, vaddr=0x0000000000000000, filesz=0x0000000000000940, memsz=0x0000000000000940, flags=0x00000006, align=0x0000000000000008
[2025-07-15 07:12:11.075] [debug] Program header 4: type=0x00000003, offset=0x0000000000004000, vaddr=0x0000000000400000, filesz=0x0000000000000015, memsz=0x0000000000000015, flags=0x00000004, align=0x0000000000000001
[2025-07-15 07:12:11.075] [debug] Program header 5: type=0x00000007, offset=0x0000000000000000, vaddr=0x0000000000000000, filesz=0x0000000000000000, memsz=0x0000000000000000, flags=0x00000004, align=0x0000000000000001
[2025-07-15 07:12:11.075] [debug] Program header 6: type=0x6474E550, offset=0x00000000003F03FC, vaddr=0x00000000007EC3FC, filesz=0x000000000002E4C4, memsz=0x000000000002E4C4, flags=0x00000004, align=0x0000000000000004
[2025-07-15 07:12:11.075] [debug] Program header 7: type=0x61000000, offset=0x00000000004F9C90, vaddr=0x0000000000000000, filesz=0x000000000008EC80, memsz=0x0000000000000000, flags=0x00000004, align=0x0000000000000010
[2025-07-15 07:12:11.075] [debug] Program header 8: type=0x6FFFFF00, offset=0x0000000000588910, vaddr=0x0000000000000000, filesz=0x0000000000000070, memsz=0x0000000000000000, flags=0x00000000, align=0x0000000000000010
[2025-07-15 07:12:11.075] [debug] Program header 9: type=0x6FFFFF01, offset=0x0000000000588980, vaddr=0x0000000000000000, filesz=0x0000000000000569, memsz=0x0000000000000000, flags=0x00000000, align=0x0000000000000010
[2025-07-15 07:12:11.075] [debug] LoadSegments: isDynamic=false, minVaddr=0x0000000000400000, maxVaddrEnd=0x0000000003A0BA08
[2025-07-15 07:12:11.076] [debug] Skipping SelfDecrypter for decrypted ELF
[2025-07-15 07:12:11.076] [debug] LoadSegments: Processing header 1 of 10, type=0x00000001
[2025-07-15 07:12:11.076] [info] LoadSegments: Processing LOAD segment 0: vaddr=0x0000000000400000, memsz=0x000000000041A8C0, filesz=0x000000000041A8C0
[2025-07-15 07:12:11.076] [info] LoadSegments: Step 1/4 - Allocating virtual memory for segment 0 (size=4MB, 4202KB, 1050 pages)
[2025-07-15 07:12:11.098] [warning] Audio buffer underrun, count=11157, queue_size=0, time_since_last=5s
[2025-07-15 07:12:16.099] [warning] Audio buffer underrun, count=12095, queue_size=0, time_since_last=5s
[2025-07-15 07:12:21.108] [warning] Audio buffer underrun, count=13034, queue_size=0, time_since_last=5s
[2025-07-15 07:12:39.968] [warning] Audio buffer underrun, count=13035, queue_size=0, time_since_last=18s
[2025-07-15 07:12:44.969] [warning] Audio buffer underrun, count=13972, queue_size=0, time_since_last=5s
