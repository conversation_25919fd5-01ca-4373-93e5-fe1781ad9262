{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake"}, {"isExternal": true, "path": "D:/vcpkg/scripts/buildsystems/vcpkg.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "D:/sss/build/CMakeFiles/3.30.5-msvc23/CMakeSystem.cmake"}, {"isExternal": true, "path": "D:/vcpkg/scripts/buildsystems/vcpkg.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/GNU-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/HP-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/LCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/XL-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/zOS-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CompilerId/VS-10.vcxproj.in"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/sss/build/CMakeFiles/3.30.5-msvc23/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CompilerId/VS-10.vcxproj.in"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/sss/build/CMakeFiles/3.30.5-msvc23/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeRCCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/sss/build/CMakeFiles/3.30.5-msvc23/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCCompilerABI.c"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/sss/build/CMakeFiles/3.30.5-msvc23/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/sss/build/CMakeFiles/3.30.5-msvc23/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2Config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FeatureSummary.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2Targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2Targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2Targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2mainTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2mainTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2mainTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/sdlfind.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindVulkan.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/libzip/libzip-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/libzip/libzip-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindBZip2.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckSymbolExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakePushCheckState.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/openssl/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindOpenSSL.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/libzip/libzip-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/libzip/libzip-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/libzip/libzip-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/imgui/imgui-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindVulkan.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/vcpkg-cmake-wrapper.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/imgui/imgui-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/imgui/imgui-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/imgui/imgui-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfigTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfigTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfigTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/nlohmann_json/nlohmann_jsonConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/nlohmann_json/nlohmann_jsonConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/nlohmann_json/nlohmann_jsonTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/portaudio/portaudioConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/portaudio/portaudioConfig.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/portaudio/portaudioTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/portaudio/portaudioTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/portaudio/portaudioTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/unofficial-spirv-reflect/unofficial-spirv-reflect-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/unofficial-spirv-reflect/unofficial-spirv-reflect-config-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/unofficial-spirv-reflect/unofficial-spirv-reflect-config-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xbyak/xbyak-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xbyak/xbyak-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xbyak/xbyak-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tbb/TBBConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tbb/TBBConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tbb/TBBTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tbb/TBBTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tbb/TBBTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tsl-robin-map/tsl-robin-mapConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tsl-robin-map/tsl-robin-mapConfig.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tsl-robin-map/tsl-robin-mapTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/toml11/toml11ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/toml11/toml11Config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/toml11/toml11Targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/Tracy/TracyConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/Tracy/TracyTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/Tracy/TracyTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/Tracy/TracyTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xxhash/xxHashConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xxhash/xxHashConfig.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xxhash/xxHashTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xxhash/xxHashTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xxhash/xxHashTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zydis/zydis-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zydis/zydis-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zycore-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zycore-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zyan-functions.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zycore-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zycore-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zycore-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zydis/zydis-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zydis/zydis-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zydis/zydis-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/openssl/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindOpenSSL.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/png/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPNG.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/elfio/elfioConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/elfio/elfioConfig.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/elfio/elfioTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/glslang/glslang-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/glslang/glslang-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/glslang/glslang-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/glslang/glslang-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/glslang/glslang-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-config.cmake"}, {"isExternal": true, "path": "D:/bin/llvm-project-main/llvm/cmake/modules/LLVM-Config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/vcpkg-cmake-wrapper.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-targets.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutovg/plutovgConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutovg/plutovgConfig.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutovg/plutovgTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutovg/plutovgTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutovg/plutovgTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/capstone/capstone-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/capstone/capstone-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/capstone/capstone-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/capstone/capstone-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/capstone/capstone-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/ffmpeg/vcpkg-cmake-wrapper.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/ffmpeg/FindFFMPEG.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}], "kind": "cmakeFiles", "paths": {"build": "D:/sss/build", "source": "D:/sss/src"}, "version": {"major": 1, "minor": 1}}