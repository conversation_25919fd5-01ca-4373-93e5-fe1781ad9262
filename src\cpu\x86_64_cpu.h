#ifndef X86_64_CPU_H
#define X86_64_CPU_H

#include <array>
#include <atomic>
#include <chrono>
#include <cstdint>
#include <immintrin.h> // For SIMD intrinsics
#include <istream>
#include <memory>
#include <mutex>
#include <ostream>
#include <queue>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>

// Include register definitions first
#include "cpu/register.h"

// Forward declarations to avoid circular dependencies
namespace ps4 {
class PS4Emulator;
class PS4MMU;
class TLB;
} // namespace ps4

namespace x86_64 {
class Device;
class APIC;
class Pipeline;
class X86_64JITCompiler;
class InstructionDecoder;
class DecodedInstruction;
} // namespace x86_64

// Include necessary headers after forward declarations
#include "cache/cache.h"
#include "cpu/decoded_instruction.h"
#include "cpu/device.h"
#include "cpu/instruction_decoder.h"
#include "cpu/x86_64_pipeline.h"
#include "emulator/apic.h"
#include "emulator/interrupt_handler.h"
#include "jit/x86_64_jit_compiler.h"
#include "memory/ps4_mmu.h"
#include "memory/tlb.h"

namespace x86_64 {

// Constants for default CPU state
const uint64_t DEFAULT_STACK_POINTER = 0x7FFFFFF000ULL; // Example stack pointer
const uint64_t DEFAULT_ENTRY_POINT = 0x1000ULL;         // Example entry point
const uint64_t DEFAULT_RFLAGS = 0x202ULL; // Default RFLAGS (IF=1, always 1)

// Segment register values (example for flat model)
const uint16_t KERNEL_CS = 0x08; // Kernel Code Segment
const uint16_t KERNEL_DS = 0x10; // Kernel Data Segment
const uint16_t USER_CS = 0x1B;   // User Code Segment
const uint16_t USER_DS = 0x23;   // User Data Segment

// Exception Vectors (Intel Manual Vol 3A, Chapter 6)
enum ExceptionVector : uint8_t {
  EXC_DE = 0x00,  // Divide Error
  EXC_DB = 0x01,  // Debug
  EXC_NMI = 0x02, // Non-Maskable Interrupt
  EXC_BP = 0x03,  // Breakpoint
  EXC_OF = 0x04,  // Overflow
  EXC_BR = 0x05,  // BOUND Range Exceeded
  EXC_UD = 0x06,  // Invalid Opcode
  EXC_NM = 0x07,  // Device Not Available (No Math Coprocessor)
  EXC_DF = 0x08,  // Double Fault
  EXC_TS = 0x0A,  // Invalid TSS
  EXC_NP = 0x0B,  // Segment Not Present
  EXC_SS = 0x0C,  // Stack-Segment Fault
  EXC_GP = 0x0D,  // General Protection
  EXC_PF = 0x0E,  // Page Fault
  EXC_MF = 0x10,  // x87 FPU Floating-Point Error
  EXC_AC = 0x11,  // Alignment Check
  EXC_MC = 0x12,  // Machine Check
  EXC_XM = 0x13,  // SIMD Floating-Point Exception
  EXC_VE = 0x14,  // Virtualization Exception
  EXC_CP = 0x15,  // Control Protection Exception
};

// RFLAGS bits
enum RFLAGS_BITS : uint64_t {
  FLAG_CF = (1ULL << 0),    // Carry Flag
  FLAG_PF = (1ULL << 2),    // Parity Flag
  FLAG_AF = (1ULL << 4),    // Auxiliary Carry Flag
  FLAG_ZF = (1ULL << 6),    // Zero Flag
  FLAG_SF = (1ULL << 7),    // Sign Flag
  FLAG_TF = (1ULL << 8),    // Trap Flag
  FLAG_IF = (1ULL << 9),    // Interrupt Enable Flag
  FLAG_DF = (1ULL << 10),   // Direction Flag
  FLAG_OF = (1ULL << 11),   // Overflow Flag
  FLAG_IOPL = (3ULL << 12), // I/O Privilege Level (bits 12-13)
  FLAG_NT = (1ULL << 14),   // Nested Task Flag
  FLAG_RF = (1ULL << 16),   // Resume Flag
  FLAG_VM = (1ULL << 17),   // Virtual-8086 Mode Flag
  FLAG_AC = (1ULL << 18),   // Alignment Check Flag
  FLAG_VIF = (1ULL << 19),  // Virtual Interrupt Flag
  FLAG_VIP = (1ULL << 20),  // Virtual Interrupt Pending Flag
  FLAG_ID = (1ULL << 21),   // ID Flag

  // Mask for arithmetic flags that are typically updated together
  ARITHMETIC_FLAGS_MASK =
      FLAG_CF | FLAG_PF | FLAG_AF | FLAG_ZF | FLAG_SF | FLAG_OF,
};

// Register and MSR enums are now defined in cpu/register.h

// Custom exception for CPU errors
struct CPUException : std::runtime_error {
  explicit CPUException(const std::string &msg) : std::runtime_error(msg) {}
};

// Helper for priority queue of interrupts
struct InterruptRequest {
  uint8_t vector;
  uint8_t priority; // Higher value means higher priority

  bool operator<(const InterruptRequest &other) const {
    return priority < other.priority;
  }
};

// Device interface is defined in cpu/device.h

// Programmable Interrupt Controller (PIC)
class PIC : public x86_64::Device {
public:
  PIC();
  void Reset() override;
  void Write(uint64_t address, uint64_t value, uint8_t size) override;
  uint64_t Read(uint64_t address, uint8_t size) override;
  std::string GetName() const override;
  void SaveState(std::ostream &out) const override;
  void LoadState(std::istream &in) override;
  void SignalInterrupt(uint8_t irq);
  void UpdateInterrupts();
  void SetCPU(std::weak_ptr<X86_64CPU> cpu) { this->cpu = cpu; }

private:
  uint8_t icw1, icw4;
  uint8_t imr; // Interrupt mask register
  uint8_t isr; // In-service register
  uint8_t irr; // Interrupt request register
  uint8_t baseVector;
  uint8_t currentCommand;
  uint8_t initSequence;
  std::weak_ptr<X86_64CPU> cpu;
};

// APIC class is defined in emulator/apic.h

// Programmable Interval Timer (PIT)
class PIT : public x86_64::Device {
public:
  PIT();
  void Reset() override;
  void Write(uint64_t address, uint64_t value, uint8_t size) override;
  uint64_t Read(uint64_t address, uint8_t size) override;
  std::string GetName() const override;
  void SaveState(std::ostream &out) const override;
  void LoadState(std::istream &in) override;
  void Tick();
  void SetCPU(std::weak_ptr<X86_64CPU> cpu) { this->cpu = cpu; }

private:
  uint16_t counters[3];
  uint8_t controlWord;
  uint8_t currentChannel;
  bool lowByte = false;
  std::weak_ptr<X86_64CPU> cpu;
};

// Device manager
class DeviceManager {
public:
  void RegisterDevice(uint64_t base, uint64_t size,
                      std::weak_ptr<x86_64::Device> device);
  std::weak_ptr<x86_64::Device> FindDevice(uint64_t address);

private:
  struct DeviceMapping {
    uint64_t base;
    uint64_t size;
    std::weak_ptr<x86_64::Device> device;
  };
  std::vector<DeviceMapping> devices;
};

// FPU State structure (simplified FXSAVE/FXRSTOR format)
struct FPUState {
  uint16_t controlWord;
  uint16_t statusWord;
  uint16_t tagWord;
  uint16_t opcode;
  uint64_t lastInstructionPointer;
  uint64_t lastDataPointer;
  std::array<double, 8> st;    // FPU stack registers
  uint8_t top;                 // Stack top pointer (3 bits)
  std::array<uint8_t, 8> tags; // Tag word expanded (2 bits per register)

  // FPU Stack Management
  void PushStack(double value) {
    top = (top - 1) & 7;
    st[top] = value;
    tags[top] = 0; // Valid
    UpdateTagWord();
  }

  double PopStack() {
    double value = st[top];
    tags[top] = 3; // Empty
    top = (top + 1) & 7;
    UpdateTagWord();
    return value;
  }

  void UpdateTagWord() {
    tagWord = 0;
    for (int i = 0; i < 8; i++) {
      tagWord |= (tags[i] & 3) << (i * 2);
    }
  }

  uint8_t GetStackIndex(uint8_t reg) const { return (top + reg) & 7; }

  bool IsStackFull() const {
    return tags[(top - 1) & 7] != 3; // Check if next position is not empty
  }

  bool IsStackEmpty() const {
    return tags[top] == 3; // Check if current top is empty
  }

  void SetStackOverflow() {
    statusWord |= 0x0040; // Stack overflow
    statusWord |= 0x0001; // Invalid operation
  }

  void SetStackUnderflow() {
    statusWord |= 0x0040; // Stack underflow
    statusWord |= 0x0001; // Invalid operation
  }
};

// MMX State structure (part of FPU state)
struct MMXState {
  bool mmxMode;
  // MMX registers are aliased with FPU registers, so no separate array needed
};

// CPU Context for saving/restoring state (e.g., for fiber switching)
struct CPUContext {
  std::array<uint64_t, 17> registers; // RAX-R15, RIP
  uint64_t rflags;
  std::array<__m256i, 16> xmmRegisters; // XMM0-XMM15
  uint64_t rip;
  uint64_t rsp;
  uint16_t cs, ds, es, fs, gs, ss;
  uint64_t cr0, cr2, cr3, cr4;
  uint8_t fpu_state_bytes[512]; // Placeholder for FPU state (FXSAVE area)
};

// VMCS structure definition (comprehensive)
struct VMCS {
  uint32_t revision_id;
  uint32_t abort_indicator;

  // Guest state area
  uint64_t guest_rip;
  uint64_t guest_rsp;
  uint64_t guest_rflags;
  uint64_t guest_cr0;
  uint64_t guest_cr3;
  uint64_t guest_cr4;
  uint64_t guest_dr7;
  uint64_t guest_rax;
  uint64_t guest_rbx;
  uint64_t guest_rcx;
  uint64_t guest_rdx;
  uint64_t guest_rsi;
  uint64_t guest_rdi;
  uint64_t guest_rbp;
  uint64_t guest_r8;
  uint64_t guest_r9;
  uint64_t guest_r10;
  uint64_t guest_r11;
  uint64_t guest_r12;
  uint64_t guest_r13;
  uint64_t guest_r14;
  uint64_t guest_r15;

  // Guest segment registers
  uint16_t guest_cs_selector;
  uint16_t guest_ds_selector;
  uint16_t guest_es_selector;
  uint16_t guest_fs_selector;
  uint16_t guest_gs_selector;
  uint16_t guest_ss_selector;
  uint16_t guest_tr_selector;
  uint16_t guest_ldtr_selector;

  uint64_t guest_cs_base;
  uint64_t guest_ds_base;
  uint64_t guest_es_base;
  uint64_t guest_fs_base;
  uint64_t guest_gs_base;
  uint64_t guest_ss_base;
  uint64_t guest_tr_base;
  uint64_t guest_ldtr_base;
  uint64_t guest_gdtr_base;
  uint64_t guest_idtr_base;

  uint32_t guest_cs_limit;
  uint32_t guest_ds_limit;
  uint32_t guest_es_limit;
  uint32_t guest_fs_limit;
  uint32_t guest_gs_limit;
  uint32_t guest_ss_limit;
  uint32_t guest_tr_limit;
  uint32_t guest_ldtr_limit;
  uint32_t guest_gdtr_limit;
  uint32_t guest_idtr_limit;

  uint32_t guest_cs_access_rights;
  uint32_t guest_ds_access_rights;
  uint32_t guest_es_access_rights;
  uint32_t guest_fs_access_rights;
  uint32_t guest_gs_access_rights;
  uint32_t guest_ss_access_rights;
  uint32_t guest_tr_access_rights;
  uint32_t guest_ldtr_access_rights;

  // Host state area
  uint64_t host_cr0;
  uint64_t host_cr3;
  uint64_t host_cr4;
  uint64_t host_rip;
  uint64_t host_rsp;
  uint16_t host_cs_selector;
  uint16_t host_ds_selector;
  uint16_t host_es_selector;
  uint16_t host_fs_selector;
  uint16_t host_gs_selector;
  uint16_t host_ss_selector;
  uint16_t host_tr_selector;
  uint64_t host_fs_base;
  uint64_t host_gs_base;
  uint64_t host_tr_base;
  uint64_t host_gdtr_base;
  uint64_t host_idtr_base;

  // VM execution control fields
  uint32_t pin_based_vm_exec_control;
  uint32_t cpu_based_vm_exec_control;
  uint32_t secondary_vm_exec_control;
  uint32_t exception_bitmap;
  uint32_t page_fault_error_code_mask;
  uint32_t page_fault_error_code_match;
  uint32_t cr3_target_count;
  uint64_t cr3_target_value0;
  uint64_t cr3_target_value1;
  uint64_t cr3_target_value2;
  uint64_t cr3_target_value3;
  uint64_t apic_access_addr;
  uint64_t virtual_apic_addr;
  uint64_t tpr_threshold;
  uint64_t ept_pointer;

  // VM exit control fields
  uint32_t vm_exit_controls;
  uint32_t vm_exit_msr_store_count;
  uint32_t vm_exit_msr_load_count;
  uint64_t vm_exit_msr_store_addr;
  uint64_t vm_exit_msr_load_addr;

  // VM entry control fields
  uint32_t vm_entry_controls;
  uint32_t vm_entry_msr_load_count;
  uint32_t vm_entry_intr_info_field;
  uint32_t vm_entry_exception_error_code;
  uint32_t vm_entry_instruction_len;
  uint64_t vm_entry_msr_load_addr;

  // VM exit information fields
  uint32_t vm_exit_reason;
  uint32_t vm_exit_qualification;
  uint64_t guest_linear_address;
  uint64_t guest_physical_address;
  uint32_t vm_exit_intr_info;
  uint32_t vm_exit_intr_error_code;
  uint32_t idt_vectoring_info_field;
  uint32_t idt_vectoring_error_code;
  uint32_t vm_exit_instruction_len;
  uint32_t vm_exit_instruction_info;

  // Additional fields
  uint64_t vmcs_link_pointer;
  uint64_t guest_ia32_debugctl;
  uint64_t guest_ia32_pat;
  uint64_t guest_ia32_efer;
  uint64_t guest_ia32_perf_global_ctrl;
  uint64_t guest_pdpte0;
  uint64_t guest_pdpte1;
  uint64_t guest_pdpte2;
  uint64_t guest_pdpte3;
  uint64_t host_ia32_pat;
  uint64_t host_ia32_efer;
  uint64_t host_ia32_perf_global_ctrl;
};

// SMM Saved State Map (comprehensive)
struct SMMSavedState {
  // Standard x86-64 SMM state save map format
  uint32_t smm_revision_id;
  uint32_t smbase;
  uint32_t smm_state_save_completion_flag;
  uint32_t reserved1;

  // General purpose registers
  uint64_t rax;
  uint64_t rcx;
  uint64_t rdx;
  uint64_t rbx;
  uint64_t rsp;
  uint64_t rbp;
  uint64_t rsi;
  uint64_t rdi;
  uint64_t r8;
  uint64_t r9;
  uint64_t r10;
  uint64_t r11;
  uint64_t r12;
  uint64_t r13;
  uint64_t r14;
  uint64_t r15;

  // Segment registers and their hidden parts
  uint16_t es_selector;
  uint16_t es_attributes;
  uint32_t es_limit;
  uint64_t es_base;

  uint16_t cs_selector;
  uint16_t cs_attributes;
  uint32_t cs_limit;
  uint64_t cs_base;

  uint16_t ss_selector;
  uint16_t ss_attributes;
  uint32_t ss_limit;
  uint64_t ss_base;

  uint16_t ds_selector;
  uint16_t ds_attributes;
  uint32_t ds_limit;
  uint64_t ds_base;

  uint16_t fs_selector;
  uint16_t fs_attributes;
  uint32_t fs_limit;
  uint64_t fs_base;

  uint16_t gs_selector;
  uint16_t gs_attributes;
  uint32_t gs_limit;
  uint64_t gs_base;

  uint16_t ldtr_selector;
  uint16_t ldtr_attributes;
  uint32_t ldtr_limit;
  uint64_t ldtr_base;

  uint16_t tr_selector;
  uint16_t tr_attributes;
  uint32_t tr_limit;
  uint64_t tr_base;

  // GDTR and IDTR
  uint32_t gdtr_limit;
  uint64_t gdtr_base;
  uint32_t idtr_limit;
  uint64_t idtr_base;

  // Control registers and other system state
  uint64_t rip;
  uint64_t rflags;
  uint64_t cr0;
  uint64_t cr3;
  uint64_t cr4;
  uint64_t dr6;
  uint64_t dr7;
  uint64_t efer;

  // IO trap state
  uint32_t io_restart_rip;
  uint32_t io_restart_rcx;
  uint32_t io_restart_rsi;
  uint32_t io_restart_rdi;
  uint8_t io_trap_flag;
  uint8_t io_restart_flag;
  uint8_t auto_halt_restart;
  uint8_t reserved2;

  // FPU state (simplified - in real implementation this would be full XSAVE
  // area)
  uint8_t fpu_state[512];

  // Additional fields for 64-bit mode
  uint64_t io_rip;
  uint64_t io_rcx;
  uint64_t io_rsi;
  uint64_t io_rdi;
  uint64_t io_restart_rip64;
  uint64_t io_restart_rcx64;
  uint64_t io_restart_rsi64;
  uint64_t io_restart_rdi64;

  // SMM state
  uint32_t smm_entry_count;
  uint64_t last_smm_entry_time;
  uint64_t last_smm_exit_time;
  uint64_t smm_total_time;
};

// VMX Constants
const uint32_t VMX_REVISION_ID = 0x00000001; // Example VMX revision ID

// SMM Constants
const uint64_t SMM_HANDLER_ENTRY_POINT = 0xFFF00000; // SMM entry point
const uint64_t SMM_BASE_DEFAULT = 0x30000;           // Default SMBASE
const uint64_t SMM_STATE_SAVE_AREA_SIZE = 0x200;     // SMM state save area size
const uint64_t SMM_CODE_SEGMENT_BASE = 0x38000;      // SMM code segment base
const uint32_t SMM_REVISION_ID = 0x00020064;         // SMM revision identifier

// SMM State Save Area offsets (from SMBASE + 0xFE00)
const uint32_t SMM_STATE_SAVE_OFFSET = 0xFE00;
const uint32_t SMM_AUTO_HALT_RESTART_OFFSET = 0xFE02;
const uint32_t SMM_IO_TRAP_OFFSET = 0xFE04;
const uint32_t SMM_IO_RESTART_RIP_OFFSET = 0xFE08;
const uint32_t SMM_IO_RESTART_RCX_OFFSET = 0xFE0C;
const uint32_t SMM_IO_RESTART_RSI_OFFSET = 0xFE10;
const uint32_t SMM_IO_RESTART_RDI_OFFSET = 0xFE14;
const uint32_t SMM_CR4_OFFSET = 0xFE18;
const uint32_t SMM_CR3_OFFSET = 0xFE20;
const uint32_t SMM_CR0_OFFSET = 0xFE28;
const uint32_t SMM_DR7_OFFSET = 0xFE30;
const uint32_t SMM_DR6_OFFSET = 0xFE38;
const uint32_t SMM_RFLAGS_OFFSET = 0xFE40;
const uint32_t SMM_RIP_OFFSET = 0xFE48;
const uint32_t SMM_R15_OFFSET = 0xFE50;
const uint32_t SMM_R14_OFFSET = 0xFE58;
const uint32_t SMM_R13_OFFSET = 0xFE60;
const uint32_t SMM_R12_OFFSET = 0xFE68;
const uint32_t SMM_R11_OFFSET = 0xFE70;
const uint32_t SMM_R10_OFFSET = 0xFE78;
const uint32_t SMM_R9_OFFSET = 0xFE80;
const uint32_t SMM_R8_OFFSET = 0xFE88;
const uint32_t SMM_RDI_OFFSET = 0xFE90;
const uint32_t SMM_RSI_OFFSET = 0xFE98;
const uint32_t SMM_RBP_OFFSET = 0xFEA0;
const uint32_t SMM_RSP_OFFSET = 0xFEA8;
const uint32_t SMM_RBX_OFFSET = 0xFEB0;
const uint32_t SMM_RDX_OFFSET = 0xFEB8;
const uint32_t SMM_RCX_OFFSET = 0xFEC0;
const uint32_t SMM_RAX_OFFSET = 0xFEC8;

// SMM Interrupt types
enum class SMMInterruptType : uint8_t {
  SMI = 0x02,           // System Management Interrupt
  NMI = 0x02,           // Non-Maskable Interrupt (can trigger SMM)
  MACHINE_CHECK = 0x12, // Machine Check Exception
  THERMAL = 0x14,       // Thermal interrupt
  PERFORMANCE = 0x15    // Performance monitoring interrupt
};

// TSS (Task State Segment) structure for x86-64
#pragma pack(push, 1)
struct TSS64 {
  uint32_t reserved1;
  uint64_t rsp0; // Stack pointer for privilege level 0
  uint64_t rsp1; // Stack pointer for privilege level 1
  uint64_t rsp2; // Stack pointer for privilege level 2
  uint64_t reserved2;
  uint64_t ist1; // Interrupt Stack Table 1
  uint64_t ist2; // Interrupt Stack Table 2
  uint64_t ist3; // Interrupt Stack Table 3
  uint64_t ist4; // Interrupt Stack Table 4
  uint64_t ist5; // Interrupt Stack Table 5
  uint64_t ist6; // Interrupt Stack Table 6
  uint64_t ist7; // Interrupt Stack Table 7
  uint64_t reserved3;
  uint16_t reserved4;
  uint16_t
      io_map_base; // I/O Map Base Address (offset to I/O permission bitmap)
};
#pragma pack(pop)

// TSS Descriptor structure
#pragma pack(push, 1)
struct TSSDescriptor {
  uint16_t limit_low;
  uint16_t base_low;
  uint8_t base_mid;
  uint8_t access;
  uint8_t granularity;
  uint8_t base_high;
  uint32_t base_upper;
  uint32_t reserved;
};
#pragma pack(pop)

// VMX Exit Reasons
enum class VMExitReason : uint32_t {
  EXCEPTION_OR_NMI = 0,
  EXTERNAL_INTERRUPT = 1,
  TRIPLE_FAULT = 2,
  INIT_SIGNAL = 3,
  SIPI = 4,
  IO_SMI = 5,
  OTHER_SMI = 6,
  INTERRUPT_WINDOW = 7,
  NMI_WINDOW = 8,
  TASK_SWITCH = 9,
  CPUID = 10,
  GETSEC = 11,
  HLT = 12,
  INVD = 13,
  INVLPG = 14,
  RDPMC = 15,
  RDTSC = 16,
  RSM = 17,
  VMCALL = 18,
  VMCLEAR = 19,
  VMLAUNCH = 20,
  VMPTRLD = 21,
  VMPTRST = 22,
  VMREAD = 23,
  VMRESUME = 24,
  VMWRITE = 25,
  VMXOFF = 26,
  VMXON = 27,
  CR_ACCESS = 28,
  DR_ACCESS = 29,
  IO_INSTRUCTION = 30,
  RDMSR = 31,
  WRMSR = 32,
  ENTRY_FAILURE_INVALID_GUEST_STATE = 33,
  ENTRY_FAILURE_MSR_LOADING = 34,
  MWAIT = 36,
  MONITOR_TRAP_FLAG = 37,
  MONITOR = 39,
  PAUSE = 40,
  ENTRY_FAILURE_MACHINE_CHECK = 41,
  TPR_BELOW_THRESHOLD = 43,
  APIC_ACCESS = 44,
  VIRTUALIZED_EOI = 45,
  GDTR_IDTR_ACCESS = 46,
  LDTR_TR_ACCESS = 47,
  EPT_VIOLATION = 48,
  EPT_MISCONFIGURATION = 49,
  INVEPT = 50,
  RDTSCP = 51,
  VMX_PREEMPTION_TIMER_EXPIRED = 52,
  INVVPID = 53,
  WBINVD = 54,
  XSETBV = 55,
  APIC_WRITE = 56,
  RDRAND = 57,
  INVPCID = 58,
  VMFUNC = 59,
  RDSEED = 61,
  XSAVES = 63,
  XRSTORS = 64
};

// VMX VMCS Field Encoding
enum class VMCSField : uint64_t {
  // 16-bit control fields
  VPID = 0x00000000,

  // 16-bit guest state fields
  GUEST_ES_SELECTOR = 0x00000800,
  GUEST_CS_SELECTOR = 0x00000802,
  GUEST_SS_SELECTOR = 0x00000804,
  GUEST_DS_SELECTOR = 0x00000806,
  GUEST_FS_SELECTOR = 0x00000808,
  GUEST_GS_SELECTOR = 0x0000080A,
  GUEST_LDTR_SELECTOR = 0x0000080C,
  GUEST_TR_SELECTOR = 0x0000080E,

  // 16-bit host state fields
  HOST_ES_SELECTOR = 0x00000C00,
  HOST_CS_SELECTOR = 0x00000C02,
  HOST_SS_SELECTOR = 0x00000C04,
  HOST_DS_SELECTOR = 0x00000C06,
  HOST_FS_SELECTOR = 0x00000C08,
  HOST_GS_SELECTOR = 0x00000C0A,
  HOST_TR_SELECTOR = 0x00000C0C,

  // 64-bit control fields
  IO_BITMAP_A = 0x00002000,
  IO_BITMAP_B = 0x00002002,
  MSR_BITMAP = 0x00002004,
  VM_EXIT_MSR_STORE_ADDR = 0x00002006,
  VM_EXIT_MSR_LOAD_ADDR = 0x00002008,
  VM_ENTRY_MSR_LOAD_ADDR = 0x0000200A,
  EXECUTIVE_VMCS_POINTER = 0x0000200C,
  TSC_OFFSET = 0x00002010,
  VIRTUAL_APIC_PAGE_ADDR = 0x00002012,
  APIC_ACCESS_ADDR = 0x00002014,
  EPT_POINTER = 0x0000201A,

  // 64-bit guest state fields
  VMCS_LINK_POINTER = 0x00002800,
  GUEST_IA32_DEBUGCTL = 0x00002802,
  GUEST_IA32_PAT = 0x00002804,
  GUEST_IA32_EFER = 0x00002806,
  GUEST_IA32_PERF_GLOBAL_CTRL = 0x00002808,
  GUEST_PDPTE0 = 0x0000280A,
  GUEST_PDPTE1 = 0x0000280C,
  GUEST_PDPTE2 = 0x0000280E,
  GUEST_PDPTE3 = 0x00002810,

  // 64-bit host state fields
  HOST_IA32_PAT = 0x00002C00,
  HOST_IA32_EFER = 0x00002C02,
  HOST_IA32_PERF_GLOBAL_CTRL = 0x00002C04,

  // 32-bit control fields
  PIN_BASED_VM_EXEC_CONTROL = 0x00004000,
  CPU_BASED_VM_EXEC_CONTROL = 0x00004002,
  EXCEPTION_BITMAP = 0x00004004,
  PAGE_FAULT_ERROR_CODE_MASK = 0x00004006,
  PAGE_FAULT_ERROR_CODE_MATCH = 0x00004008,
  CR3_TARGET_COUNT = 0x0000400A,
  VM_EXIT_CONTROLS = 0x0000400C,
  VM_EXIT_MSR_STORE_COUNT = 0x0000400E,
  VM_EXIT_MSR_LOAD_COUNT = 0x00004010,
  VM_ENTRY_CONTROLS = 0x00004012,
  VM_ENTRY_MSR_LOAD_COUNT = 0x00004014,
  VM_ENTRY_INTR_INFO_FIELD = 0x00004016,
  VM_ENTRY_EXCEPTION_ERROR_CODE = 0x00004018,
  VM_ENTRY_INSTRUCTION_LEN = 0x0000401A,
  TPR_THRESHOLD = 0x0000401C,
  SECONDARY_VM_EXEC_CONTROL = 0x0000401E,

  // 32-bit read-only data fields
  VM_INSTRUCTION_ERROR = 0x00004400,
  VM_EXIT_REASON = 0x00004402,
  VM_EXIT_INTERRUPTION_INFO = 0x00004404,
  VM_EXIT_INTERRUPTION_ERROR_CODE = 0x00004406,
  IDT_VECTORING_INFO_FIELD = 0x00004408,
  IDT_VECTORING_ERROR_CODE = 0x0000440A,
  VM_EXIT_INSTRUCTION_LEN = 0x0000440C,
  VMX_INSTRUCTION_INFO = 0x0000440E,

  // 32-bit guest state fields
  GUEST_ES_LIMIT = 0x00004800,
  GUEST_CS_LIMIT = 0x00004802,
  GUEST_SS_LIMIT = 0x00004804,
  GUEST_DS_LIMIT = 0x00004806,
  GUEST_FS_LIMIT = 0x00004808,
  GUEST_GS_LIMIT = 0x0000480A,
  GUEST_LDTR_LIMIT = 0x0000480C,
  GUEST_TR_LIMIT = 0x0000480E,
  GUEST_GDTR_LIMIT = 0x00004810,
  GUEST_IDTR_LIMIT = 0x00004812,
  GUEST_ES_AR_BYTES = 0x00004814,
  GUEST_CS_AR_BYTES = 0x00004816,
  GUEST_SS_AR_BYTES = 0x00004818,
  GUEST_DS_AR_BYTES = 0x0000481A,
  GUEST_FS_AR_BYTES = 0x0000481C,
  GUEST_GS_AR_BYTES = 0x0000481E,
  GUEST_LDTR_AR_BYTES = 0x00004820,
  GUEST_TR_AR_BYTES = 0x00004822,
  GUEST_INTERRUPTIBILITY_INFO = 0x00004824,
  GUEST_ACTIVITY_STATE = 0x00004826,
  GUEST_SMBASE = 0x00004828,
  GUEST_SYSENTER_CS = 0x0000482A,
  GUEST_PREEMPTION_TIMER = 0x0000482E,

  // 32-bit host state fields
  HOST_SYSENTER_CS = 0x00004C00,

  // Natural width control fields
  CR0_GUEST_HOST_MASK = 0x00006000,
  CR4_GUEST_HOST_MASK = 0x00006002,
  CR0_READ_SHADOW = 0x00006004,
  CR4_READ_SHADOW = 0x00006006,
  CR3_TARGET_VALUE0 = 0x00006008,
  CR3_TARGET_VALUE1 = 0x0000600A,
  CR3_TARGET_VALUE2 = 0x0000600C,
  CR3_TARGET_VALUE3 = 0x0000600E,

  // Natural width read-only data fields
  EXIT_QUALIFICATION = 0x00006400,
  IO_RCX = 0x00006402,
  IO_RSI = 0x00006404,
  IO_RDI = 0x00006406,
  IO_RIP = 0x00006408,
  GUEST_LINEAR_ADDRESS = 0x0000640A,

  // Natural width guest state fields
  GUEST_CR0 = 0x00006800,
  GUEST_CR3 = 0x00006802,
  GUEST_CR4 = 0x00006804,
  GUEST_ES_BASE = 0x00006806,
  GUEST_CS_BASE = 0x00006808,
  GUEST_SS_BASE = 0x0000680A,
  GUEST_DS_BASE = 0x0000680C,
  GUEST_FS_BASE = 0x0000680E,
  GUEST_GS_BASE = 0x00006810,
  GUEST_LDTR_BASE = 0x00006812,
  GUEST_TR_BASE = 0x00006814,
  GUEST_GDTR_BASE = 0x00006816,
  GUEST_IDTR_BASE = 0x00006818,
  GUEST_DR7 = 0x0000681A,
  GUEST_RSP = 0x0000681C,
  GUEST_RIP = 0x0000681E,
  GUEST_RFLAGS = 0x00006820,
  GUEST_PENDING_DBG_EXCEPTIONS = 0x00006822,
  GUEST_SYSENTER_ESP = 0x00006824,
  GUEST_SYSENTER_EIP = 0x00006826,

  // Natural width host state fields
  HOST_CR0 = 0x00006C00,
  HOST_CR3 = 0x00006C02,
  HOST_CR4 = 0x00006C04,
  HOST_FS_BASE = 0x00006C06,
  HOST_GS_BASE = 0x00006C08,
  HOST_TR_BASE = 0x00006C0A,
  HOST_GDTR_BASE = 0x00006C0C,
  HOST_IDTR_BASE = 0x00006C0E,
  HOST_SYSENTER_ESP = 0x00006C10,
  HOST_SYSENTER_EIP = 0x00006C12,
  HOST_RSP = 0x00006C14,
  HOST_RIP = 0x00006C16
};

const size_t GENERAL_REGISTER_COUNT = 17; // RAX-R15, RIP
const size_t SEGMENT_REGISTER_COUNT = 6;  // ES, CS, SS, DS, FS, GS
const size_t CONTROL_REGISTER_COUNT = 5; // CR0, CR2, CR3, CR4 (CR1 is reserved)
const size_t DEBUG_REGISTER_COUNT = 6;   // DR0-DR3, DR6, DR7
const size_t XMM_REGISTER_COUNT = 16;    // XMM0-XMM15
const size_t ZMM_REGISTER_COUNT = 32;    // ZMM0-ZMM31
const size_t K_REGISTER_COUNT = 8;       // K0-K7

const size_t TLB_SIZE = 256;               // Number of TLB entries
const size_t INSTRUCTION_BUFFER_SIZE = 16; // Max instruction length + padding
const size_t MAX_INSTRUCTION_LENGTH = 15;  // Max x86-64 instruction length
const size_t MAX_REP_ITERATIONS = 1000000; // Max iterations for REP string ops

const uint64_t MWAIT_MAX_CYCLES = 1000000; // Example MWAIT timeout

class X86_64CPU : public std::enable_shared_from_this<X86_64CPU> {
public:
  X86_64CPU(ps4::PS4Emulator &emulator, ps4::PS4MMU &mmu, uint32_t cpuId);
  ~X86_64CPU();

  // CPU Lifecycle
  bool Initialize();
  void Shutdown();
  void ResetState();
  void ExecuteCycle();
  void Execute(); // Single instruction execution

  // Register Access
  uint64_t GetRegister(Register r) const;
  bool SetRegister(Register r, uint64_t v);
  uint64_t GetRflags() const;
  void SetRflags(uint64_t f);
  bool GetFlag(uint64_t m) const;
  void SetFlag(uint64_t flag, bool value);

  // Control Register Access
  uint64_t GetControlRegister(Register r) const;
  void SetControlRegister(Register r, uint64_t v);
  uint64_t GetCR0() const;
  uint64_t GetCR2() const;
  uint64_t GetCR3() const;
  uint64_t GetCR4() const;
  void SetCR0(uint64_t v);
  void SetCR2(uint64_t v);
  void SetCR3(uint64_t v);
  void SetCR4(uint64_t v);

  // Debug Register Access
  uint64_t GetDebugRegister(Register r) const;
  void SetDebugRegister(Register r, uint64_t v);

  // MSR Access
  uint64_t GetMSR(MSR msr_index) const;
  void SetMSR(MSR msr_index, uint64_t value);

  // Segment Register Access
  uint16_t GetCS() const;
  void SetCS(uint16_t v);
  uint16_t GetSS() const;
  void SetSS(uint16_t v);
  uint16_t GetDS() const;
  void SetDS(uint16_t v);
  uint16_t GetES() const;
  void SetES(uint16_t v);
  uint16_t GetFS() const;
  void SetFS(uint16_t v);
  uint16_t GetGS() const;
  void SetGS(uint16_t v);

  // Descriptor Table Register Access
  void SetGDTR(uint64_t base, uint16_t limit);
  void SetIDTR(uint64_t base, uint16_t limit);
  void SetLDTR(uint16_t selector);
  void SetTR(uint16_t selector);
  uint64_t GetGDTRBase() const;
  uint16_t GetGDTRLimit() const;
  uint64_t GetIDTRBase() const;
  uint16_t GetIDTRLimit() const;
  uint16_t GetLDTR() const;
  uint16_t GetTR() const;
  uint64_t GetTSSBase() const;
  uint16_t GetTSSLimit() const;
  uint16_t GetKernelSS() const;

  // Privilege Level
  uint8_t GetCPL() const;
  uint8_t GetIOPL() const;

  // Memory Management Unit (MMU) and TLB
  ps4::PS4MMU &GetMemory();
  ps4::PS4MMU &GetMMU() { return GetMemory(); }
  uint64_t TranslateAddress(uint64_t virtualAddr);
  void InvalidateTLB(uint64_t virtAddr);

  // Interrupts
  void QueueInterrupt(uint8_t vector, uint8_t priority);
  void TriggerInterrupt(uint8_t vector, uint64_t errorCode,
                        bool isSoftwareInterrupt);
  void TriggerInterrupt(uint8_t vector); // Simple overload for no error code

  // I/O Port Access
  uint64_t ReadIOPort(uint16_t port, uint8_t size);
  void WriteIOPort(uint16_t port, uint64_t value, uint8_t size);

  // Pipeline support methods
  uint64_t
  CalculateMemoryAddress(const DecodedInstruction::Operand &operand) const;
  void Compare(uint64_t op1, uint64_t op2, uint8_t size);
  void Push(uint64_t value, uint8_t sizeInBytes = 8);
  uint64_t Pop(uint8_t sizeInBytes = 8);

  // State Management
  void SaveState(std::ostream &out);
  void LoadState(std::istream &in);
  void SetContext(const CPUContext &ctx);
  bool IsRunning() const;

  // Diagnostics
  std::unordered_map<std::string, uint64_t> GetDiagnostics() const;
  float GetUtilization() const { return utilization; }

  // APIC Access
  x86_64::APIC &GetAPIC() { return *apic; }
  const x86_64::APIC &GetAPIC() const { return *apic; }

  // XMM Register Access
  __m256i GetXMMRegister(uint8_t reg) const;
  void SetXMMRegister(uint8_t reg, const __m256i &value);

  // Mask Register Access (AVX-512)
  uint16_t GetMaskRegister(uint8_t reg) const;
  void SetMaskRegister(uint8_t reg, uint16_t value);

  // Arithmetic flags (make public)
  bool CheckCondition(uint8_t conditionCode);
  void UpdateArithmeticFlags(uint64_t op1, uint64_t op2, uint64_t result,
                             uint8_t sizeInBits, bool isSubtract);

  // JIT and Pipeline Access
  X86_64JITCompiler &GetJITCompiler();
  Pipeline &GetPipeline();

  // Process Management
  uint64_t GetProcessId() const;
  void SetProcessId(uint64_t pid);

  // CPU Identification
  uint32_t GetCPUId() const;

  // Fiber Management
  bool SwitchToFiber(uint64_t fiberId);

private:
  // Private helper functions for internal register access (no mutex)
  uint64_t _getRegister(Register r) const;
  void _setRegister(Register r, uint64_t v);

  // Internal CPU state
  ps4::PS4Emulator &m_emulator;
  ps4::PS4MMU &mmu;
  uint32_t m_cpuId;

  // Registers
  std::array<uint64_t, GENERAL_REGISTER_COUNT> registers;
  uint64_t rflags;
  std::array<uint16_t, SEGMENT_REGISTER_COUNT> segmentRegisters;
  std::array<uint64_t, CONTROL_REGISTER_COUNT> controlRegisters;
  std::array<uint64_t, DEBUG_REGISTER_COUNT> debugRegisters;
  std::unordered_map<uint32_t, uint64_t> msrRegisters;
  // SIMD Registers
  std::array<__m256i, XMM_REGISTER_COUNT> xmmRegisters; // XMM/YMM registers
  std::array<__m512i, ZMM_REGISTER_COUNT> zmmRegisters; // ZMM registers
  std::array<uint16_t, K_REGISTER_COUNT> kRegisters;    // K registers (mask)

  // FPU State
  FPUState fpuState;
  MMXState mmxState;

  // Descriptor Table Registers
  uint64_t gdtrBase;
  uint16_t gdtrLimit;
  uint64_t idtrBase;
  uint16_t idtrLimit;
  uint16_t ldtr;
  uint16_t tr;
  uint64_t tssBase;
  uint16_t tssLimit;
  uint16_t ioPermissionBitmapOffset;
  std::vector<uint8_t> ioPermissionBitmap;
  uint16_t kernelSS; // Kernel stack segment for SYSCALL/SYSRET

  // CPU Control
  bool running;
  bool halted; // True if CPU is in HLT state
  uint64_t processId;

  // Components
  std::unique_ptr<X86_64JITCompiler> jit;
  std::unique_ptr<InstructionDecoder> decoder;
  std::unique_ptr<Pipeline> pipeline;
  ps4::TLB tlb;

  // Devices
  DeviceManager deviceManager;
  std::shared_ptr<PIC> pic;
  std::shared_ptr<x86_64::APIC> apic;
  std::shared_ptr<PIT> pit;

  // Interrupt Queue
  std::priority_queue<InterruptRequest> interruptQueue;

  // Mutex for thread safety
  mutable std::recursive_timed_mutex mutex;

  // Performance metrics
  float utilization;
  std::chrono::steady_clock::time_point lastCycleStart;

  // MWAIT state
  bool mwaitState = false;
  uint64_t mwaitMonitorAddr = 0;
  uint64_t mwaitSavedValue = 0;
  uint64_t mwaitWakeupCounter = 0;

  // VMX state
  bool vmxEnabled = false;
  bool inVMXNonRootOperation = false;
  uint64_t vmxonRegion = 0;
  uint64_t currentVMCS = 0; // Physical address of current VMCS
  VMCS vmcs;                // In-memory representation of current VMCS
  VMCS vmcsState;           // Saved VMCS state for VMCLEAR

  // SMM state
  bool inSMM = false;
  uint64_t smbase = SMM_BASE_DEFAULT;
  SMMSavedState smmSavedState;
  uint64_t smmEntryCount = 0;
  uint64_t smmTotalTime = 0;
  std::chrono::high_resolution_clock::time_point smmEntryTime;
  std::chrono::high_resolution_clock::time_point smmExitTime;
  bool smmIoRestart = false;
  bool smmAutoHaltRestart = false;
  uint8_t smmInterruptType = 0;

  // CPU instruction dispatch table for efficient instruction execution
  using CPUInstructionHandler =
      std::function<void(const DecodedInstruction &, uint64_t &)>;
  std::unordered_map<x86_64::InstructionType, CPUInstructionHandler>
      cpuOpcodeDispatchTable;

  // Private helper methods
  void InitializeCPUOpcodeDispatchTable();
  void HandleUnhandledInstruction(const DecodedInstruction &instr,
                                  uint64_t &nextRip);
  void UpdateUtilization(const std::chrono::steady_clock::time_point &start);
  bool CalculateParity(uint64_t value);
  void UpdateLogicalFlags(uint64_t result, uint8_t sizeInBits);
  uint64_t ReadOperandValue(const DecodedInstruction::Operand &operand);
  void WriteOperandValue(const DecodedInstruction::Operand &operand,
                         uint64_t value);
  void WriteMemoryOperand(const DecodedInstruction::Operand &operand,
                          uint64_t value);
  __m256i ReadXmmOperandValue(const DecodedInstruction::Operand &operand);
  void WriteXmmOperandValue(const DecodedInstruction::Operand &operand,
                            const __m256i &value);
  void WriteXmmOperand(const DecodedInstruction::Operand &operand,
                       const __m256i &value); // Helper for WriteOperandValue
  void UpdateFlags(uint64_t result, uint64_t op1, uint64_t op2,
                   uint8_t sizeInBits, bool isSubtract);
  bool CheckPagePermissions(uint64_t pte_entry, uint8_t cpl, bool is_write,
                            bool is_execute) const;
  void DeviceTick();
  void FetchDecodeExecute();
  void UpdateStringIndexes(uint8_t size, bool forward = true);
  void ExecuteStringOperation(const DecodedInstruction &instr,
                              uint64_t &nextRip);
  bool ValidateIOPortAccess(uint16_t port, uint8_t size);

  // I/O Permission Bitmap Management
  void LoadIOPermissionBitmap();
  bool CheckIOPermissionBitmap(uint16_t port, uint8_t size);
  void SetIOPermissionBit(uint16_t port, bool allow);
  void SetIOPermissionRange(uint16_t startPort, uint16_t endPort, bool allow);
  void ClearIOPermissionBitmap();
  void UpdateIOPermissionBitmapOffset(uint16_t offset);
  void LoadTSSDescriptor();

  // FPU/SIMD/VMX/SMM instruction handlers
  void ExecuteFloatingPointInstruction(const DecodedInstruction &instr,
                                       uint64_t &nextRip);
  void ExecuteAVXInstruction(const DecodedInstruction &instr,
                             uint64_t &nextRip);
  void ExecuteAVX512Instruction(const DecodedInstruction &instr,
                                uint64_t &nextRip);

  // VMX instructions
  void VMXON(uint64_t region);
  void VMCLEAR(uint64_t region);
  void VMPTRLD(uint64_t region);
  void VMPTRST(uint64_t addr);
  void VMREAD(uint64_t field, uint64_t &value);
  void VMWRITE(uint64_t field, uint64_t value);
  void VMLAUNCH();
  void VMRESUME();
  void VMXOFF();
  void INVEPT(uint64_t type, uint64_t descriptor);
  void INVVPID(uint64_t type, uint64_t descriptor);

  // VM exit handling
  void VMExit(uint32_t reason, uint64_t qualification = 0);
  void SaveGuestState();
  void LoadHostState();
  bool CheckVMExitConditions(const DecodedInstruction &instr);

  // SMM instructions and management
  void EnterSMM(SMMInterruptType type = SMMInterruptType::SMI);
  void ExitSMM();
  void RSM(); // Resume from SMM
  void SaveSMMState();
  void RestoreSMMState();
  void WriteSMMStateToMemory();
  void ReadSMMStateFromMemory();
  void SetSMBASE(uint64_t newBase);
  uint64_t GetSMBASE() const;
  bool IsSMMActive() const;
  void TriggerSMI(SMMInterruptType type = SMMInterruptType::SMI);

  // Power Management
  void ExecuteMWAIT();
  void CheckMWAITWakeup();

  // Cache Control
  void CLFLUSH(uint64_t addr);
  void PREFETCH(DecodedInstruction::Operand::Type hint, uint64_t addr);
};

} // namespace x86_64

#endif // X86_64_CPU_H
